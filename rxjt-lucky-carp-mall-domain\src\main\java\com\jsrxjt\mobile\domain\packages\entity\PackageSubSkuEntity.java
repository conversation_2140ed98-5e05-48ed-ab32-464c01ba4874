package com.jsrxjt.mobile.domain.packages.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 套餐子sku实体类
 * @Author: ywt
 * @Date: 2025-06-19 14:59
 * @Version: 1.0
 */
@Data
public class PackageSubSkuEntity {

    private Long id;
    /**
     * 套餐spuId
     */
    private Long packageSpuId;
    /**
     * 套餐skuid
     */
    private Long packageSkuId;
    /**
     * 平台卡券id(支付宝对应活动编码)
     */
    private String outerId;
    /**
     * 面值名称
     */
    private String amountName;

    /**
     * 面值
     */
    private BigDecimal amount;

    /**
     * 卡管售价
     */
    private BigDecimal price;

    /**
     * 卡管成本价
     */
    private BigDecimal costPrice;

    /**
     * 手续费百分比
     */
    private BigDecimal commissionFee;

    /**
     * 福鲤圈的售价
     */
    private BigDecimal platformPrice;

    /**
     * 每月每人单卡限购数量
     */
    private Integer limitNumPerMonth;

    /**
     * 库存预警数量
     */
    private Integer stockWarnNum;

    /**
     * 已售数量
     */
    private Integer soldNum;

    /**
     * 子sku状态 0:下架 1:出售中
     */
    private Integer subSkuStatus;

    /**
     * 卡券库存
     */
    private Integer inventory;

    /**
     * 规格值id，多个规格值用逗号分隔
     */
    private String specsValue;

    /**
     * 备注富文本
     */
    private String remark;

    /**
     * 是否多次核销 0否 1是
     */
    private Integer isMoreOffset;

    /**
     * 起售数量
     */
    private Integer onSaleNum;

    /**
     * 限售数量
     */
    private Integer rationSaleNum;

    /**
     * 卡券有效期
     */
    private Date couponValidTime;

    /**
     * 卡管平台状态0:下架 1:出售中
     */
    private Integer centerStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否手动下架卡券(0:否 1:是)
     */
    private Integer manualDown;

    /**
     * 删除标志0--正常，1--删除
     */
    private Integer delFlag;
    /**
     * 卡券核销类型 101:平台自发券 201:卡号+卡密 202:卡号或卡密 203:卡号+卡密+校验码 301:链接 302:链接+验证码 303:链接+卡号+验证码 304:卡号+短链接
     */
    private Integer type;

    /**
     * 自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式)',
     */
    private Integer flqType;
    /**
     * 充值类型,0其他 1手机号 2QQ号
     */
    private Integer accountType;
    
    /**
     * 该套餐中的子sku数量
     */
    private Integer packageCouponNum;

    /**
     * 系统卡券类型：1:普通卡券 3:品诺
     */
    private Integer couponType;

    /**
     * 套餐子sku图片
     */
    private String imgUrl;

    /**
     * 套餐子sku圆形图片
     */
    private String circularImgUrl;
}
