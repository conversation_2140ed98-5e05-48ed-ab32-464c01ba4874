package com.jsrxjt.mobile.infra.message.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.message.entity.MessageCatEntity;
import com.jsrxjt.mobile.domain.message.entity.MessageDetailEntity;
import com.jsrxjt.mobile.domain.message.entity.MessageUserEntity;
import com.jsrxjt.mobile.domain.message.repository.MessageCatRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageDetailRepository;
import com.jsrxjt.mobile.infra.message.mapper.MessageDetailMapper;
import com.jsrxjt.mobile.infra.message.mapper.MessageUserMapper;
import com.jsrxjt.mobile.infra.message.po.MessageDetailPO;


import com.jsrxjt.mobile.infra.message.po.MessageUserSystemPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
@Slf4j
public class MessageDetailRepositoryImpl implements MessageDetailRepository {

    private final MessageDetailMapper messageDetailMapper;
    private final MessageCatRepository messageCatRepository;
    private final MessageUserMapper messageUserMapper;
    private final CustomerRepository customerRepository;


    /**
     * 分页查询所有消息内容
     *
     * @param current 页码
     * @param size    每页条数
     * @return 分页消息列表
     */
    @Override
    public PageDTO<MessageDetailEntity> findAllByMessage(Long current, Long size) {
        // 创建分页对象
        Page<MessageDetailPO> resultPage = buildPage(null, current, size);
        if (resultPage == null || CollectionUtil.isEmpty(resultPage.getRecords())) {
            return PageDTO.emptyBuild(current, size);
        }

        // 获取所有分类ID
        List<Integer> catIds = resultPage.getRecords().stream()
                .map(MessageDetailPO::getCatId)
                .distinct()
                .collect(Collectors.toList());

        // 查询分类信息
        List<MessageCatEntity> allByCatIdList = messageCatRepository.findAllByCatIdList(catIds);
        Map<Integer, MessageCatEntity> catMap = allByCatIdList.stream()
                .collect(Collectors.toMap(MessageCatEntity::getCatId, cat -> cat));

        // 转换为Entity对象，并设置分类信息
        List<MessageDetailEntity> records = resultPage.getRecords().stream().map(po -> {
            MessageDetailEntity entity = new MessageDetailEntity();
            BeanUtils.copyProperties(po, entity);

            // 设置分类信息
            if (po.getCatId() != null && catMap.containsKey(po.getCatId())) {
                MessageCatEntity cat = catMap.get(po.getCatId());
                entity.setCatName(cat.getCatName());
            }
            return entity;
        }).collect(Collectors.toList());

        return PageDTO.build(records, resultPage.getTotal(), resultPage.getSize(), resultPage.getCurrent());
    }

    /**
     * 查询用户创建后的消息内容
     *
     * @param customerId 客户id
     * @param current    页码
     * @param size       每页条数
     * @return 分页消息列表
     */
    @Override
    public PageDTO<MessageDetailEntity> findAllByUserTime(Long customerId, Long current, Long size) {
        if (customerId == null) {
            return PageDTO.emptyBuild(current, size);
        }
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null) {
            return PageDTO.emptyBuild(current, size);
        }
        // 创建分页对象

        Date createTime = customerEntity.getCreateTime();
        // 执行分页查询
        Page<MessageDetailPO> resultPage = buildPage(createTime, current, size);
        if (CollectionUtil.isEmpty(resultPage.getRecords())) {
            return PageDTO.emptyBuild(current, size);
        }
        // 获取所有分类ID
        List<Integer> catIds = resultPage.getRecords().stream()
                .map(MessageDetailPO::getCatId)
                .distinct()
                .collect(Collectors.toList());

        // 查询分类信息
        List<MessageCatEntity> allByCatIdList = messageCatRepository.findAllByCatIdList(catIds);
        Map<Integer, MessageCatEntity> catMap = allByCatIdList.stream()
                .collect(Collectors.toMap(MessageCatEntity::getCatId, cat -> cat));

        // 转换为Entity对象，并设置分类信息
        List<MessageDetailEntity> records = resultPage.getRecords().stream().map(po -> {
            MessageDetailEntity entity = new MessageDetailEntity();
            BeanUtils.copyProperties(po, entity);

            // 设置分类信息
            if (po.getCatId() != null && catMap.containsKey(po.getCatId())) {
                MessageCatEntity cat = catMap.get(po.getCatId());
                entity.setCatName(cat.getCatName());
            }
            return entity;
        }).collect(Collectors.toList());

        return PageDTO.build(records, resultPage.getTotal(), resultPage.getSize(), resultPage.getCurrent());
    }

    /**
     * @param id
     * @return
     */
    @Override
    public MessageDetailEntity findById(Long id) {
        MessageDetailPO po = messageDetailMapper.selectById(id);
        if (po != null) {
            return BeanUtil.copyProperties(po, MessageDetailEntity.class);
        }
        return null;
    }

    /**
     * @param messageId
     * @param customerId
     * @return
     */
    @Override
    public MessageUserEntity getMessageUser(Long messageId, Long customerId) {
        LambdaQueryWrapper<MessageUserSystemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageUserSystemPO::getMessageId, messageId)
                .eq(MessageUserSystemPO::getCustomerId, customerId)
                .last("LIMIT 1");
        MessageUserSystemPO po = messageUserMapper.selectOne(queryWrapper);
        if (po == null) {
            return null;
        }
        return BeanUtil.copyProperties(po, MessageUserEntity.class);
    }

    /**
     * @param messageIdList
     * @return
     */
    @Override
    public List<MessageUserEntity> getMessageUserByMsgIdList(List<Long> messageIdList, Long customerId) {
        LambdaQueryWrapper<MessageUserSystemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MessageUserSystemPO::getMessageId, messageIdList);
        queryWrapper.eq(MessageUserSystemPO::getCustomerId, customerId);
        List<MessageUserSystemPO> messageUserSystemPOS = messageUserMapper.selectList(queryWrapper);

        return BeanUtil.copyToList(messageUserSystemPOS, MessageUserEntity.class);
    }

    @Override
    public void saveMessageUser(Long messageId, Long customerId) {
        MessageUserSystemPO messageUserSystemPO = new MessageUserSystemPO();
        messageUserSystemPO.setMessageId(messageId);
        messageUserSystemPO.setCustomerId(customerId);
        messageUserSystemPO.setCreateTime(new Date());
        try {
            messageUserMapper.insert(messageUserSystemPO);
        } catch (Exception e) {
            log.error("保存系统消息已读失败", e);
        }
    }

    @Override
    public int updateSysReadNum(Long messageId) {
        if (Objects.isNull(messageId)) {
            return 0;
        }
        return messageDetailMapper.updateSysReadNum(messageId);
    }

    /**
     * 批量更新瑞祥消息已读
     *
     * @param customerId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysIsReadBatch(Long customerId) {
        if (customerId == null) {
            return;
        }
        //获取未读的瑞祥消息
        List<Long> notReadSysMsg = messageDetailMapper.getNotReadSysMsg(customerId);
        if (CollectionUtil.isEmpty(notReadSysMsg)) {
            return;
        }
        List<MessageUserSystemPO> poList = new ArrayList<>();
        for (Long msgId : notReadSysMsg) {
            MessageUserSystemPO po = new MessageUserSystemPO();
            po.setMessageId(msgId);
            po.setCustomerId(customerId);
            po.setCreateTime(new Date());
            poList.add(po);
        }
        messageUserMapper.saveAll(poList);
    }

    /**
     * 瑞祥消息数量
     *
     * @param customerId
     */
    @Override
    public int countSysMsg(Long customerId) {
        if (customerId == null) {
            return 0;
        }
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null) {
            return 0;
        }
        // 创建分页对象

        Date createTime = customerEntity.getCreateTime();
        return messageDetailMapper.countSysNotRead(customerId, createTime);
    }


    private Page<MessageDetailPO> buildPage(Date createTime, Long page, Long size) {
        // 创建分页对象
        Page<MessageDetailPO> pageParam = new Page<>(page, size);
        // 构建查询条件
        LambdaQueryWrapper<MessageDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageDetailPO::getStatus, 1)  // 状态为启用
                .eq(MessageDetailPO::getDelFlag, 0)  // 未删除
                .ge(createTime != null, MessageDetailPO::getModTime, createTime)
                .orderByDesc(MessageDetailPO::getSort, MessageDetailPO::getModTime);

        // 执行分页查询
        Page<MessageDetailPO> resultPage = messageDetailMapper.selectPage(pageParam, queryWrapper);
        return resultPage;
    }
}
