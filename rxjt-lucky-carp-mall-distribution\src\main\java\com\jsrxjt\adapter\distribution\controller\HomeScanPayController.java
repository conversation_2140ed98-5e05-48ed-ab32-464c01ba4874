package com.jsrxjt.adapter.distribution.controller;

import com.jsrxjt.common.adapter.annotation.SignParam;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.api.scanPay.request.PollPayRefundRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.UserHomePayRequestDTO;
import com.jsrxjt.mobile.api.scanPay.response.*;
import com.jsrxjt.mobile.api.scanPay.PosOrderRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.OfflineScanCodeRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.PollPrePayRequestDTO;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanPayService;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssService;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 首页扫码付/展码付
 * @Author: zy
 * @Date: 2025-05-30 10:08
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/homeScan")
@Tag(name = "线下扫码付", description = "线下扫码付接口")
public class HomeScanPayController {

    private final HomeScanPayService homeScanPayService;

    private final PickScanCaseService pickScanCaseService;

    private final ScanPayWssService scanPayWssService;

    /**
     * 根据定位城市获取首页扫码付/展码付应用
     */
    @PostMapping("/getChannelApp")
    @Operation(summary = "根据定位城市获取首页扫码付/展码付应用列表")
    @VerifySign(hasToken = true)
    public BaseResponse<List<PickChannelAppResponseDTO>> getChannelApp(@RequestBody @Valid PickChannelAppRequestDTO requestDTO){
        return homeScanPayService.getChannelApp(requestDTO);
    }

    /**
     * 第三方系统调用-根据code查询用户信息
     * @param requestDTO
     * @return
     */
    @PostMapping("/getCustomerByCode")
    @Operation(summary = "第三方系统调用-根据code查询用户信息")
    @VerifySign(hasToken = false)
    public BaseResponse<CustomerDetailResponse> getCustomerEntityByCode(@RequestBody @Valid PickCodeRequestDTO requestDTO){
        return BaseResponse.succeed(homeScanPayService.getCustomerEntityByCode(requestDTO));
    }

    @PostMapping("/getOfflineCode")
    @Operation(summary = "获取线下付款码")
    @VerifySign(hasToken = true)
    public BaseResponse<HomeScanNumberResponseDTO> getOfflineCode(@RequestBody @Valid OfflineScanCodeRequestDTO request) {
        return BaseResponse.succeed(homeScanPayService.getOfflineCode(request));
    }
    /**
     * 收银台返回订单信息
     * @return
     */
    @PostMapping("/open/getThirdVipOrder")
    @Operation(summary = "收银台返回订单信息")
    @SignParam
    public BaseResponse<ThirdOrderInfoResponseDTO> getCashierVipOrder(@RequestBody PosOrderRequestDTO requestDTO){
        ThirdOrderInfoResponseDTO cashierVipOrder = homeScanPayService.getCashierVipOrder(requestDTO);
        return BaseResponse.succeed(cashierVipOrder);
    }
    /**
     * 轮询预付订单展示付款界面
     * @return
     */
    @PostMapping("/open/pollPreOrder")
    @Operation(summary = "预支付信息-前端唤起支付")
    @SignParam
    public BaseResponse<PollVipOrderResponseDTO> pollPrePay(@RequestBody PollPrePayRequestDTO requestDTO){
        return BaseResponse.succeed(homeScanPayService.pollPreOrder(requestDTO));
    }

    @PostMapping("/pollPaymentRefund")
    @Operation(summary = "轮询支付结果")
    @VerifySign(hasToken = true)
    public BaseResponse<PollOrderRefundResultResponseDTO> pollPaymentRefund(@RequestBody PollPayRefundRequestDTO requestDTO){
        return BaseResponse.succeed(homeScanPayService.pollPaymentRefund(requestDTO));
    }


    /**
     * 扫码付款
     * @return
     */
    @PostMapping("/homeScanPay")
    @Operation(summary = "用户展码支付")
    @VerifySign(hasToken = true)
    public BaseResponse<HomePayResultResponseDTO> homeScanPay(@RequestBody UserHomePayRequestDTO requestDTO){
        return BaseResponse.succeed(homeScanPayService.homeScanPay(requestDTO));
    }


    /**
     * webscoket 推送
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/open/pushWssInfo")
    @Operation(summary = "websocket 推送")
    @SignParam
    public BaseResponse pushWssInfo(@RequestBody @Valid PickChannelWssRequestDTO requestDTO){
        return scanPayWssService.pushWssInfo(requestDTO);
    }

    /**
     * 卡消费记录
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/open/cardTradeList")
    @Operation(summary = "卡消费记录")
    @SignParam
    public BaseResponse<List<PickCardTradeListResponseDTO>> cardTradeList(@RequestBody @Valid PickCardTradeListRequestDTO requestDTO){
        return pickScanCaseService.cardTradeList(requestDTO.getCardNo());
    }

    /**
     * 创建订单(外部系统调用)
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/open/createChannelOrder")
    @Operation(summary = "创建订单(外部系统调用)")
    @SignParam(signType=1)
    public PickBaseResponse<PickChannelOrderResponseDTO> createChannelOrder(@RequestBody @Valid PickChannelOrderRequestDTO requestDTO){
        PickBaseResponse<PickChannelOrderResponseDTO> pickBaseResponse = pickScanCaseService.createChannelOrder(requestDTO);
        log.info("创建订单返回内容: {}", pickBaseResponse);
        return pickBaseResponse;
    }

    @PostMapping("/open/orderConfirm")
    @Operation(summary = "提货券分销中台交易确认(与订单变更请求一致,不需要对接)")
    @SignParam(signType=1)
    public PickBaseResponse<PickChannelOrderResponseDTO>orderConfirm(@RequestBody @Valid PickChannelOrderConfirmRequestDTO requestDTO){
        return pickScanCaseService.orderConfirm(requestDTO);
    }

    @PostMapping("/open/refundChannelOrder")
    @Operation(summary = "订单退款")
    @SignParam(signType=1)
    public PickBaseResponse<PickChannelOrderRefundResponseDTO>refundChannelOrder(@RequestBody PickChannelOrderRefundRequestDTO requestDTO){
        PickBaseResponse<PickChannelOrderRefundResponseDTO> pickBaseResponse =  pickScanCaseService.refundChannelOrder(requestDTO);
        log.info("订单退款返回内容: {}", pickBaseResponse);
        return pickBaseResponse;
    }

    @PostMapping("/open/changeChannelOrder")
    @Operation(summary = "订单变更")
    @SignParam(signType=1)
    public PickBaseResponse<PickChannelOrderResponseDTO>changeChannelOrder(@RequestBody PickChannelOrderConfirmRequestDTO requestDTO){
        return pickScanCaseService.orderConfirm(requestDTO);
    }

    @PostMapping("/open/getChannelOrder")
    @Operation(summary = "支付状态查询 暂时用于测试环境")
    @SignParam(signType=1)
    public PickBaseResponse<PickChannelOrderResultResponseDTO>getChannelOrder(@RequestBody PickChannelOrderStatusRequestDTO requestDTO){
        return pickScanCaseService.getChannelOrderPayStatus(requestDTO);
    }

    @PostMapping("/open/getChannelRefundStatus")
    @Operation(summary = "退款状态查询")
    @SignParam(signType=1)
    public PickBaseResponse<PickOrderRefundStatusResponseDTO>getChannelRefundStatus(@RequestBody PickOrderRefundStatusRequestDTO requestDTO){
        return pickScanCaseService.getRefundStatus(requestDTO);
    }


    /**
     * 订单退款结果回告[幂等性]
     */
    @PostMapping("/getPaymentOrderRefund")
    @Operation(summary = "订单退款结果回告[幂等性] ")
    public BaseResponse<PickOrderRefundResultResponseDTO> getPaymentOrderRefund(@RequestBody PickPlatformPayRefundRequest request){
        return BaseResponse.succeed(pickScanCaseService.getPaymentOrderRefund(request));
    }
}
