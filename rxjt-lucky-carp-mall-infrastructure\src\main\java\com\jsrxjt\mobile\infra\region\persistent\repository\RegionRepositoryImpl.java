package com.jsrxjt.mobile.infra.region.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.infra.region.persistent.mapper.HotCityMapper;
import com.jsrxjt.mobile.infra.region.persistent.mapper.RegionMapper;
import com.jsrxjt.mobile.infra.region.persistent.po.HotCityPO;
import com.jsrxjt.mobile.infra.region.persistent.po.RegionPO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class RegionRepositoryImpl implements RegionRepository {

    private final RegionMapper regionMapper;

    private final HotCityMapper hotCityMapper;

    private final RedisTemplate redisTemplate;

    @Override
    public List<RegionEntity> getAllCityAndDistrict() {
        List<RegionPO> list = regionMapper.selectList(new LambdaQueryWrapper<RegionPO>()
                .eq(RegionPO::getRegionType, 2)
                .or()
                .eq(RegionPO::getRegionType, 3)
                .eq(RegionPO::getDelFlag, 0));
        return toEntityList(list);
    }

    @Override
    public List<RegionEntity> getAllHotCity() {
        List<HotCityPO> list = hotCityMapper.selectList(new LambdaQueryWrapper<HotCityPO>()
                .eq(HotCityPO::getDelFlag, 0)
                .orderByDesc(HotCityPO::getSort));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Long> regionIds = list.stream().map(HotCityPO::getCityId).collect(Collectors.toList());
        List<RegionPO> regionPOList = regionMapper.selectBatchIds(regionIds);
        Map<Integer, RegionPO> regionIdToRegionPOMap = regionPOList.stream().collect(Collectors.toMap(RegionPO::getId, po -> po));
        return list.stream().map(po -> {
            RegionEntity entity = new RegionEntity();
            entity.setId(po.getCityId().intValue());
            entity.setRegionName(po.getCityName());
            RegionPO regionPO = regionIdToRegionPOMap.get(po.getCityId().intValue());
            if (regionPO != null) {
                entity.setLat(regionPO.getLatitude().stripTrailingZeros().toPlainString());
                entity.setLng(regionPO.getLongitude().stripTrailingZeros().toPlainString());
                entity.setRegionType(regionPO.getRegionType());
            }
            return entity;
        }).collect(Collectors.toList());
    }

    @Override
    public RegionEntity getNearestCityByLocation(String lat, String lng) {
        RegionPO regionPO = regionMapper.getNearestCityByLocation(lat, lng);
        RegionEntity entity = new RegionEntity();
        BeanUtil.copyProperties(regionPO, entity);
        return entity;
    }

    private static List<RegionEntity> toEntityList(List<RegionPO> poList) {
        if (CollectionUtil.isEmpty(poList)) {
            return Collections.emptyList();
        }
        List<RegionEntity> regionEntities = poList.stream().map(po -> {
            RegionEntity entity = new RegionEntity();
            BeanUtil.copyProperties(po, entity);
            entity.setLat(po.getLatitude().stripTrailingZeros().toPlainString());
            entity.setLng(po.getLongitude().stripTrailingZeros().toPlainString());
            return entity;
        }).collect(Collectors.toList());
        return regionEntities;
    }

    @Override
    public RegionEntity getCurrentRegion(Long customerId) {
        if (customerId == null) {
            throw new RuntimeException("未获取到用户信息");
        }
        String customerLocationKey = RedisKeyConstants.REGION_LOCATION_CUSTOMER + customerId;
        Object location = redisTemplate.opsForValue().get(customerLocationKey);
        if (location == null) {
            throw new BizException(Status.NOT_LOCATE.getCode(), "未获取到用户位置信息");
        }
        return (RegionEntity) location;
    }

    @Override
    public RegionEntity getRegionById(Integer regionId) {
        Object cityData = redisTemplate.opsForHash().get(RedisKeyConstants.REGION_CITY, regionId.toString());
        if (cityData != null) {
            return (RegionEntity) cityData;
        } else {
            RegionPO regionPO = regionMapper.selectById(regionId);
            if (regionPO != null) {
                RegionEntity region = BeanUtil.copyProperties(regionPO, RegionEntity.class);
                region.setLng(regionPO.getLongitude().stripTrailingZeros().toPlainString());
                region.setLat(regionPO.getLatitude().stripTrailingZeros().toPlainString());

                if (region.getRegionType() != null && region.getRegionType() <= 3) {
                    redisTemplate.opsForHash().put(RedisKeyConstants.REGION_CITY, regionId.toString(), region);
                }
                return region;
            }
        }
        return null;
    }

    @Override
    public List<RegionEntity> getRegionsByParentRegionId(Integer parentRegionId) {
        List<RegionPO> list = regionMapper.selectList(new LambdaQueryWrapper<RegionPO>()
                .eq(RegionPO::getParentId, parentRegionId)
                .eq(RegionPO::getDelFlag, 0));
        return BeanUtil.copyToList(list, RegionEntity.class);
    }

    @Override
    public List<RegionEntity> getAllRegionByRegionType(Integer regionType) {
        List<RegionPO> list = regionMapper.selectList(new LambdaQueryWrapper<RegionPO>()
                .eq(RegionPO::getRegionType, regionType)
                .eq(RegionPO::getDelFlag, 0));
        return toEntityList(list);
    }
}
