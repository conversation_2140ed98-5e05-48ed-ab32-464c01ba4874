package com.jsrxjt.mobile.domain.product.service.factory.builder.impl;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsRepository;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsSkuRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductChannelEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductChannelRepository;
import com.jsrxjt.mobile.domain.product.service.factory.builder.ProductItemBuilder;
import com.jsrxjt.mobile.domain.promotion.service.PromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 卡券类型ProductItem构建器
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/27
 */
@Component("couponProductItemBuilder")
@RequiredArgsConstructor
@Slf4j
public class CouponProductItemBuilder implements ProductItemBuilder {

    private final CouponGoodsRepository couponGoodsRepository;
    private final CouponGoodsSkuRepository couponGoodsSkuRepository;

    private final ProductBrandRepository productBrandRepository;

    private final ProductChannelRepository productChannelRepository;

    private final PromotionService promotionService;

    @Override
    public ProductItem build(ProductItemId productItemId) {
        Long spuId = productItemId.getSpuId();
        Long skuId = productItemId.getSkuId();

        // 1. 查询SPU信息
        CouponGoodsEntity spuEntity = couponGoodsRepository.getCouponInfo(spuId);
        if (spuEntity == null) {
            throw new IllegalArgumentException("未找到SPU ID为 " + spuId + " 的卡券商品");
        }

        // 2. 查询SKU信息（如果有）
        CouponGoodsSkuEntity skuEntity = null;
        if (skuId != null && skuId > 0) {
            List<CouponGoodsSkuEntity> couponGoodsSkus = couponGoodsSkuRepository.getCouponGoodsSku(spuId);
            for (CouponGoodsSkuEntity couponGoodsSku : couponGoodsSkus) {
                if (couponGoodsSku.getCouponSkuId().equals(skuId)) {
                    skuEntity = couponGoodsSku;
                    break;
                }
            }
            if (skuEntity == null) {
                throw new IllegalArgumentException("未找到SKU ID为 " + skuId + " 的卡券SKU");
            }
        }

        // 3. 构建ProductItem
        return buildFromCouponData(spuEntity, skuEntity);
    }

    private ProductItem buildFromCouponData(CouponGoodsEntity spuEntity, CouponGoodsSkuEntity skuEntity) {
        ProductItem productItem = new ProductItem();
        // 设置标识
        productItem.setSpuId(spuEntity.getCouponSpuId());
        productItem.setSkuId(skuEntity != null ? skuEntity.getCouponSkuId() : 0L);
        productItem.setProductType(ProductTypeEnum.COUPON.getType()); // 卡券类型
        productItem.setFlatProductType(100 + spuEntity.getCouponType()); // 扁平化类型

        // 设置商品基本信息
        productItem.setProductName(spuEntity.getCouponSpuName());
        productItem.setProductLogo(spuEntity.getLogoUrl());
        productItem.setImgUrl(spuEntity.getImgUrl());
        productItem.setFirstCategoryId(Long.valueOf(spuEntity.getFirstCatId()));
        productItem.setCategoryId(Long.valueOf(spuEntity.getSecondCatId()));

        productItem.setBrandId(spuEntity.getBrandId());
        ProductBrandEntity brand = productBrandRepository.findById(spuEntity.getBrandId());
        if (brand != null) {
            productItem.setBrandName(brand.getBrandName());
        }
        productItem.setChannelId(spuEntity.getChannelId());
        ProductChannelEntity channel = productChannelRepository.findById(spuEntity.getChannelId());
        if (channel != null) {
            productItem.setChannelName(channel.getChannelName());
        }
        productItem.setPayType(spuEntity.getPayType());

        productItem.setNeedCaptcha(spuEntity.getNeedCaptcha());

        productItem.setSourceTable("coupon_goods");

        // 设置价格和库存信息
        if (skuEntity != null) {

            productItem.setPlatformPrice(skuEntity.getPlatformPrice());
            productItem.setDefaultServiceFeePercent(skuEntity.getCommissionFee());
            productItem.setCostPrice(skuEntity.getCostPrice());
            productItem.setInventory(skuEntity.getInventory());
            productItem.setLimitNumPerMonth(skuEntity.getLimitNumPerMonth());
            productItem.setOutProductId(skuEntity.getCouponPlatformId());
            productItem.setSpecsValue(skuEntity.getSpecsValue());
            productItem.setFaceAmount(skuEntity.getAmount());
            productItem.setFaceName(skuEntity.getAmountName());
            productItem.setStatus((spuEntity.getStatus() == 1 && skuEntity.getStatus() == 1) ? 1 : 0);

            PromotionSkuInfo inProcessPromotionInfo = promotionService
                    .getInProcessPromotionInfo(skuEntity.getCouponSkuId(), ProductTypeEnum.COUPON.getType());
            productItem.setPromotionSkuInfo(inProcessPromotionInfo);
        }

        log.info("成功构建卡券ProductItem: spuId={}, skuId={}", spuEntity.getCouponSpuId(),
                skuEntity != null ? skuEntity.getCouponSkuId() : 0);

        return productItem;
    }
}