package com.jsrxjt.mobile.biz.region.service;

import com.jsrxjt.mobile.api.region.dto.request.UserLocationRequest;
import com.jsrxjt.mobile.api.region.dto.response.AllCityResponse;
import com.jsrxjt.mobile.api.region.dto.response.SearchCityResponse;

import java.util.List;

public interface RegionCaseService {
    List<SearchCityResponse> searchCity(String searchQuery);

    AllCityResponse allCity();

    SearchCityResponse userLocation(UserLocationRequest request);

    void updateAllRegionCache();

    SearchCityResponse getRegion(Integer regionId);
}
