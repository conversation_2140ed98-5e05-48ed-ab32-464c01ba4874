package com.jsrxjt.mobile.infra.gateway.distribution.adapter;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.exception.DistributionApiException;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistOrderDetailQueryRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistRefundResultNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistVerifySignRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistConfigResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistOrderDetailQueryResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistRefundResultNotifyResponse;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.gateway.distribution.config.DistributionConfig;
import com.jsrxjt.mobile.infra.gateway.distribution.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

/**
 * <AUTHOR> Fengping
 * @since 2025/3/24
 **/
@Slf4j
public abstract class AbstractDistributionChannelAdapter implements DistributionChannelAdapter {
    protected final HttpClientGateway httpClientGateway;

    protected final DistributionConfig.ChannelConfig config;

    protected static final String STATUS_KEY = "status";

    protected static final String CODE_KEY = "code";

    protected static final Integer SUCCESS_CODE = 200;

    protected static final String MESSAGE_KEY = "message";

    // 转换时间格式，美团使用 yyyy-MM-dd hh:mm:ss 格式
    protected static final DateTimeFormatter DAY_TIME_SPLIT_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    protected static final DateTimeFormatter COMPACT_DAY_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Autowired
    protected AbstractDistributionChannelAdapter(HttpClientGateway httpClientGateway,
            DistributionConfig distributionConfig) {
        this.httpClientGateway = httpClientGateway;
        this.config = getDistributionChanelConfig(distributionConfig);
    }

    public DistConfigResponse getDistributionChannelConfig(){
        return DistConfigResponse.builder()
                .appId(config.getAppId())
                .syncRefundFlag(config.getSyncRefundFlag())
                .build();
    }

    public boolean verifySign(DistVerifySignRequest request){
        Object verifySignDTO = request.getVerifySignDTO();
        Map<String, Object> params = JSONObject.parseObject(JSONUtil.toJsonStr(verifySignDTO), Map.class);
        log.info("待验签的参数为:{}",JSONUtil.toJsonStr(params));
        String signature = String.valueOf(params.get("signature"));
        String checkSign;
        if (request.getChannelType().equals(DistChannelType.WALMART) || request.getChannelType().equals(DistChannelType.ELEME)){
            checkSign = SignUtil.signAddKey(params, config.getAppSecret());
        } else {
            checkSign = SignUtil.sign(params, config.getAppSecret());
        }
        log.info("验签的签名值为:{}",checkSign);
        return Objects.equals(signature, checkSign);
    }
    /**
     * 获取分销配置
     *
     * @param distributionConfig 分销配置
     * @return 平台配置
     */
    protected abstract DistributionConfig.ChannelConfig getDistributionChanelConfig(
            DistributionConfig distributionConfig);

    /**
     * 通用支付成功回调通知实现
     * 
     * @param request           支付通知请求参数
     * @param dateTimeFormatter 时间格式化器
     * @return 支付通知响应
     */
    protected DistPaidNotifyResponse doPaidNotify(DistPaidNotifyRequest request,
            DateTimeFormatter dateTimeFormatter) {
        try {
            // 校验必填参数
            if (StringUtils.isBlank(request.getDistOrderNo()) || StringUtils.isBlank(request.getDistTradeNo())
                    || request.getTradeAmount() == null || request.getPayTime() == null) {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(400)
                        .message("必填参数不能为空")
                        .build();
            }

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("orderNo", request.getDistOrderNo());
            params.put("tradeNo", request.getDistTradeNo());
            params.put("tradeAmount", request.getTradeAmount().toString());

            // 转换时间格式，使用传入的格式化器
            params.put("payTime", request.getPayTime().format(dateTimeFormatter));

            // 添加公共参数和签名
            addCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getApiPath() + config.getCashierCallbackPath();
            String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                return DistPaidNotifyResponse.builder()
                        .success(true)
                        .status(SUCCESS_CODE)
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            } else {
                return DistPaidNotifyResponse.builder()
                        .success(false)
                        .status(result.getInteger(STATUS_KEY))
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("{}支付回调通知异常: {}", request.getChannelType().name(), e.getMessage(), e);
            return DistPaidNotifyResponse.builder()
                    .success(false)
                    .status(500)
                    .message(request.getChannelType().name() + "支付回调通知异常: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 通用退款结果通知实现
     * 
     * @param request           退款通知请求参数
     * @param dateTimeFormatter 时间格式化器
     * @param paramBuilder      构建参数的自定义回调函数，用于处理不同渠道的参数差异
     * @return 退款通知响应
     */
    protected DistRefundResultNotifyResponse doRefundResultNotify(DistRefundResultNotifyRequest request,
            DateTimeFormatter dateTimeFormatter,
            BiConsumer<Map<String, Object>, DistRefundResultNotifyRequest> paramBuilder) {
        try {
            // 校验基础必填参数，具体的额外参数校验由调用方自行处理
            if (StringUtils.isBlank(request.getDistTradeNo()) || StringUtils.isBlank(request.getDistRefundNo()) ||
                    request.getRefundAmount() == null || request.getStatus() == null) {
                return DistRefundResultNotifyResponse.builder()
                        .success(false)
                        .status(400)
                        .message("必填参数不能为空")
                        .build();
            }

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();

            // 使用自定义参数构建器，处理不同渠道的参数差异
            if (paramBuilder != null) {
                paramBuilder.accept(params, request);
            } else {
                // 默认参数构建逻辑
                params.put("tradeNo", request.getDistTradeNo());
                params.put("refundNo", request.getDistRefundNo());
                params.put("refundAmount", request.getRefundAmount().toString());
                params.put(STATUS_KEY, request.getStatus());

                // 默认处理退款时间
                if (request.getRefundTime() != null) {
                    params.put("refundTime", request.getRefundTime().format(dateTimeFormatter));
                } else {
                    params.put("refundTime", "");
                }
            }

            // 添加公共参数和签名
            addCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getApiPath() + config.getRefundCallbackPath();
            String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                return DistRefundResultNotifyResponse.builder()
                        .success(true)
                        .status(SUCCESS_CODE)
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            } else {
                return DistRefundResultNotifyResponse.builder()
                        .success(false)
                        .status(result.getInteger(STATUS_KEY))
                        .message(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("{}退款结果通知异常: {}", request.getChannelType().name(), e.getMessage(), e);
            return DistRefundResultNotifyResponse.builder()
                    .success(false)
                    .status(500)
                    .message(request.getChannelType().name() + "退款结果通知异常: " + e.getMessage())
                    .build();
        }
    }

    protected void addCommonParams(Map<String, Object> params) {
        params.put("appid", config.getAppId());
        params.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        params.put("nounce", generateNonce());
        String signature = SignUtil.sign(params, config.getAppSecret());
        params.put("signature", signature);

    }

    protected void otherAddCommonParams(Map<String, Object> params) {
        params.put("appid", config.getAppId());
        params.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        params.put("nounce", generateNonce());
        String signature = SignUtil.signAddKey(params, config.getAppSecret());
        params.put("signature", signature);

    }

    /**
     * 生成随机字符串
     *
     * @return 8-32位随机字符串
     */
    private String generateNonce() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    protected DistOrderDetailQueryResponse doQueryOrderDetail(DistOrderDetailQueryRequest request,
                                                              BiConsumer<Map<String, Object>, DistOrderDetailQueryRequest> paramBuilder,
                                                              BiFunction<JSONObject, String, DistOrderDetailQueryResponse> responseBuilder) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();

            // 使用自定义参数构建器，处理不同渠道的参数差异
            if (paramBuilder != null) {
                paramBuilder.accept(params, request);
            } else {
                // 默认参数构建逻辑 - 可以查询分销订单号或接入方订单号
                if (StringUtils.isNotBlank(request.getDistOrderNo())) {
                    params.put("orderNo", request.getDistOrderNo());
                }
                if (StringUtils.isNotBlank(request.getOutOrderNo())) {
                    params.put("thirdOrderNo", request.getOutOrderNo());
                }
            }

            // 参数校验 - 分销订单号和接入方订单号不能同时为空
            if (params.isEmpty() || (!params.containsKey("orderNo") && !params.containsKey("thirdOrderNo"))) {
                return DistOrderDetailQueryResponse.builder()
                        .success(false)
                        .errorCode("PARAM_ERROR")
                        .errorMessage("分销订单号和接入方订单号不能同时为空")
                        .build();
            }

            // 添加公共参数和签名
            addCommonParams(params);

            // 发送请求
            String url = config.getBaseUrl() + config.getApiPath() + config.getOrderQueryPath();
            String response = httpClientGateway.doPost(url, params, config.getConnectTimeout(),
                    config.getReadTimeout());
            JSONObject result = JSON.parseObject(response);

            // 解析响应
            if (Objects.equals(result.getInteger(STATUS_KEY), SUCCESS_CODE)) {
                // 如果提供了自定义响应构建器，使用它
                if (responseBuilder != null) {
                    return responseBuilder.apply(result, request.getChannelType().name());
                } else {
                    // 默认响应解析逻辑
                    JSONObject data = result.getJSONObject("data");
                    JSONObject order = data.getJSONObject("order");

                    return DistOrderDetailQueryResponse.builder()
                            .success(true)
                            .distOrderNo(order.getString("order_no"))
                            .outOrderNo(order.getString("store_order_no"))
                            .tradeAmount(new BigDecimal(order.getString("trade_amount")))
                            .detailPageUrl(order.getString("detail_url"))
                            .payStatus(order.getString("pay_status"))
                            .orderStatus(order.getString("order_status"))
                            .createTime(order.getString("create_time"))
                            .build();
                }
            } else {
                return DistOrderDetailQueryResponse.builder()
                        .success(false)
                        .errorCode(String.valueOf(result.getInteger(STATUS_KEY)))
                        .errorMessage(result.getString(MESSAGE_KEY))
                        .build();
            }
        } catch (Exception e) {
            log.error("{}订单详情查询异常: {}", request.getChannelType().name(), e.getMessage(), e);
            return DistOrderDetailQueryResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .errorMessage(request.getChannelType().name() + "订单详情查询异常: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 用于不支持订单详情查询的渠道，抛出统一的异常
     * 
     * @param channelName 渠道名称
     * @return 不会有返回值，会抛出异常
     */
    protected DistOrderDetailQueryResponse doThrowUnsupportedOrderDetailQuery(String channelName) {
        throw new DistributionApiException(channelName + "不支持订单详情查询");
    }

}
