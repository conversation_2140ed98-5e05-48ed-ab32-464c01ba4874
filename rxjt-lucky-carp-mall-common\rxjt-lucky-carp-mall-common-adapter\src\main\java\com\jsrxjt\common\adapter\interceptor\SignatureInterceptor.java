package com.jsrxjt.common.adapter.interceptor;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jsrxjt.common.adapter.annotation.SignParam;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.SpringContextHolder;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SignatureInterceptor
 * 基于API接口参数签名的拦截器，实现参数签名验证、时间戳验证、nonce防重放攻击
 */
@SuppressWarnings("NullableProblems")
@Slf4j
public class SignatureInterceptor implements HandlerInterceptor {

    /**
     * 签名算法类型
     */
    public enum SignAlgorithm {
        MD5
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 检查是否需要跳过签名验证
        if (isIgnoreSign()) {
            return true;
        }

        // 检查是否有@SignParam注解
        SignParam annotation = getSignParamAnnotation((HandlerMethod) handler);
        if (annotation == null) {
            // 没有该注解，不用验签
            return true;
        }
        //是否验签
        boolean isSign = annotation.isSign();
        //是否是提货分销验签
        int isPick = annotation.signType();
        if (isPick==1) {
            isSign = true;
        }
        log.info("接口路径: {}, 开始签名验证", request.getRequestURI());

        // 获取请求参数
        Map<String, String> params = getRequestParams(request);
        
        // 验证必要参数
        validateRequiredParams(params, isSign,isPick);
        
        // 验证时间戳
        validateTimestamp(params);
        
        // 验证nonce防重放
        validateNonce(params);

        // 验证签名
        if (!isSign) {
            validateSignature(params, request, isPick);
        }
        log.info("接口路径: {}, 签名验证通过", request.getRequestURI());
        return true;
    }



    /**
     * 检查是否忽略签名验证
     */
    private static boolean isIgnoreSign() {
        Environment env = SpringContextHolder.getBean(Environment.class);
        String property = env.getProperty("signature.ignore", "false");
        return "true".equals(property);
    }

    /**
     * 获取@SignParam注解
     */
    private static SignParam getSignParamAnnotation(HandlerMethod handlerMethod) {
        Method method = handlerMethod.getMethod();
        Class<?> clazz = method.getDeclaringClass();
        
        // 优先使用方法上的注解
        SignParam methodAnnotation = method.getAnnotation(SignParam.class);
        if (methodAnnotation != null) {
            return methodAnnotation;
        }
        
        // 如果没有方法注解，检查类注解
        return clazz.getAnnotation(SignParam.class);
    }

    /**
     * 获取请求参数
     */
    private Map<String, String> getRequestParams(HttpServletRequest request) throws IOException {
        Map<String, String> params = new HashMap<>();
        
        // 获取GET参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            if (CharSequenceUtil.isNotBlank(paramValue)) {
                params.put(paramName, paramValue);
            }
        }
        
        // 获取POST JSON参数
        String contentType = request.getContentType();
        if (contentType != null && contentType.contains("application/json")) {
            JSONObject jsonBody = getRequestBody(request);
            if (jsonBody != null && !jsonBody.isEmpty()) {
                for (String key : jsonBody.keySet()) {
                    Object value = jsonBody.get(key);
                    if (value != null) {
                        params.put(key, value.toString());
                    }
                }
            }
        }
        
        return params;
    }

    /**
     * 获取请求体JSON
     */
    private JSONObject getRequestBody(HttpServletRequest request) throws IOException {
        try (InputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder buffer = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }

            String content = buffer.toString();
            if (CharSequenceUtil.isBlank(content) || !JSON.isValid(content)) {
                return new JSONObject();
            }

            return JSON.parseObject(content, Feature.OrderedField);
        }
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(Map<String, String> params, Boolean isSign, int isPick) {
        // 检查必要参数
        String[] requiredParams = {"timestamp", "nonce", "sign"};
        if (!isSign) {
            requiredParams = new String[]{"timestamp", "nonce"};
        }
        for (String requiredParam : requiredParams) {
            if (!params.containsKey(requiredParam) || CharSequenceUtil.isBlank(params.get(requiredParam))) {
                throw new BizException(Status.NULL_PARAM.getCode(), 
                    "缺少必要参数: " + requiredParam);
            }
        }
    }

    /**
     * 验证时间戳
     */
    private void validateTimestamp(Map<String, String> params) {
        String timestampStr = params.get("timestamp");
        try {
            long timestamp = Long.parseLong(timestampStr);
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - timestamp * 1000);
            
            // 默认5分钟有效期
            Environment env = SpringContextHolder.getBean(Environment.class);
            long expireTime = Long.parseLong(env.getProperty("signature.timestamp.expire", "300000")); // 5分钟
            
            if (timeDiff > expireTime) {
                throw new BizException(Status.SIGN_TIME_OUT.getCode(), 
                    "请求已过期，时间戳: " + timestamp + ", 当前时间: " + currentTime);
            }
        } catch (NumberFormatException e) {
            throw new BizException(Status.NULL_PARAM.getCode(), "时间戳格式错误: " + timestampStr);
        }
    }

    /**
     * 验证nonce防重放
     */
    private void validateNonce(Map<String, String> params) {
        String nonce = params.get("nonce");
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        
        String nonceKey = RedisKeyConstants.SIGN_NONCE + nonce;
        if (redisUtil.exists(nonceKey)) {
            throw new BizException(Status.SIGN_REPEAT.getCode(), "重复请求，nonce: " + nonce);
        }
        
        // 设置nonce缓存，防止重放攻击
        redisUtil.setAdd(nonceKey, "1", 60); // 1分钟过期
    }

    /**
     * 验证签名
     */
    private void validateSignature(Map<String, String> params, HttpServletRequest request, int isPick) {
        // 获取签名密钥
        Environment env = SpringContextHolder.getBean(Environment.class);
        if(isPick==1) {
            String appid = env.getProperty("pick.appId");
            Assert.notNull(appid, "提货分销appId不能为空");
            String secretKey = env.getProperty("pick.appsecret");
            Assert.notNull(secretKey, "提货分销appsecret不能为空");
        }else{
            String secretKey = env.getProperty("signature.secret.key");
            Assert.notNull(secretKey, "签名密钥不能为空");
        }
        String receivedSign = params.get("sign");
        // 移除sign参数，用于计算签名
        Map<String, String> signParams = new HashMap<>(params);
        signParams.remove("sign");
        
        // 按字典序排序参数
        String sortedParamStr = buildSortedParamString(signParams,isPick);

        // 获取签名算法
        String algorithmStr = env.getProperty("signature.algorithm", "MD5");
        SignAlgorithm algorithm = SignAlgorithm.valueOf(algorithmStr.toUpperCase());
        
        // 计算签名
        String calculatedSign = calculateSignature(sortedParamStr, algorithm);
        
        log.debug("待签名字符串: {}", sortedParamStr);
        log.debug("计算得到的签名: {}", calculatedSign);
        log.debug("接收到的签名: {}", receivedSign);
        
        if (!Objects.equals(calculatedSign, receivedSign)) {
            throw new BizException(Status.SIGN_VERIFY_FAILED.getCode(), 
                "签名验证失败，期望: " + calculatedSign + ", 实际: " + receivedSign);
        }
    }

    /**
     * 构建排序后的参数字符串
     */
    private String buildSortedParamString(Map<String, String> params,int isPick) {
        // 按字典序排序
        List<String> sortedKeys = params.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < sortedKeys.size(); i++) {
            String key = sortedKeys.get(i);
            String value = params.get(key);
            if (i > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }
        // 添加密钥
        Environment env = SpringContextHolder.getBean(Environment.class);
        String secretKey = env.getProperty("signature.secret.key");
        if(isPick==1) {
            String appid = env.getProperty("pick.appId");
            if (CharSequenceUtil.isNotBlank(appid)) {
                sb.append("&app_id=").append(appid);
            }
            secretKey = env.getProperty("pick.appsecret");
        }

        if (CharSequenceUtil.isNotBlank(secretKey)) {
            sb.append("&key=").append(secretKey);
        }
        
        return sb.toString();
    }

    /**
     * 计算签名
     */
    private String calculateSignature(String paramStr,SignAlgorithm algorithm) {
        switch (algorithm) {
            case MD5:
                return DigestUtil.md5Hex(paramStr);
            default:
                throw new IllegalArgumentException("不支持的签名算法: " + algorithm);
        }
    }


} 