package com.jsrxjt.mobile.biz.module.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.util.geo.GeoUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.config.types.SystemConfigTypeEnum;
import com.jsrxjt.mobile.api.module.dto.request.TabPageRequest;
import com.jsrxjt.mobile.api.module.dto.response.ModuleDetailResponse;
import com.jsrxjt.mobile.api.module.dto.response.PageResponse;
import com.jsrxjt.mobile.api.module.types.ModuleCodeEnum;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.biz.module.ModuleCaseService;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalGoodsListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalGoodsListResponse;
import com.jsrxjt.mobile.domain.locallife.gateway.LocalLifeGateWay;
import com.jsrxjt.mobile.domain.locallife.request.ActivityListRequest;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeGoodsResponse;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeProductGoodsDetailVO;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeProductInfoVO;
import com.jsrxjt.mobile.domain.locallife.response.SubstationResponse;
import com.jsrxjt.mobile.domain.locallife.types.LocalLifeGoodsEnum;
import com.jsrxjt.mobile.domain.module.entity.*;
import com.jsrxjt.mobile.domain.module.repository.*;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailFactory;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.swing.plaf.synth.Region;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ModuleCaseServiceImpl implements ModuleCaseService {

    private final RedisUtil redisUtil;

    private final PageRegionRepository pageRegionRepository;

    private final PageInfoRepository pageInfoRepository;

    private final ModuleRegionRelationalRepository moduleRegionRelationalRepository;

    private final ModuleInfoRepository moduleInfoRepository;

    private final ModuleDetailRepository moduleDetailRepository;

    private final GlobalGateWay globalGateWay;

    private final LocalLifeGateWay localLifeGateWay;

    private final ModuleDetailFactory moduleDetailFactory;

    private final RegionRepository regionRepository;

    private final ConfigRepository configRepository;

    private final ProductRiskControlService productRiskControlService;

    @Override
    public List<PageResponse> page(Integer activityId, Integer regionId) {
        Integer pageId = null;
        if (activityId.equals(0)){
            //首页需要获取当前发布页
            PageInfoEntity  pageInfoEntity = pageInfoRepository.getCurrentHomePage();
            pageId = pageInfoEntity.getPageId();
        }
        Integer cityId = 0;
        Integer districtId = 0;
        RegionEntity selectRegionEntity = regionRepository.getRegionById(regionId);
        if (selectRegionEntity == null){
            log.error("区域不存在");
            return Collections.emptyList();
        }
        if (selectRegionEntity.getRegionType().equals(2)){
            cityId = selectRegionEntity.getId();
        }else if (selectRegionEntity.getRegionType().equals(3)) {
            cityId = selectRegionEntity.getParentId();
            districtId = selectRegionEntity.getId();
        }else {
            log.error("区域类型错误,使用全国页面 regionId = {}", regionId);
        }
        //根据区域获取page_region
        PageRegionEntity pageRegionEntity = pageRegionRepository.getPageRegion(activityId, pageId, cityId, districtId);
        if (pageRegionEntity != null && pageRegionEntity.getPageRegionId() != null){
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            String key = RedisKeyConstants.PAGE_INFO + pageRegionId;
            String pageInfo = redisUtil.get(key);
            List<PageResponse> pageResponseList = new ArrayList<>();
            //不包含全球购和本地生活tab的组件
            if (StrUtil.isNotBlank(pageInfo)){
                pageResponseList.addAll(JSONUtil.toList(pageInfo, PageResponse.class));
            }else {
                List<PageResponse> basePageResponseList = this.getPageInfo(pageRegionId);
                if (CollectionUtil.isNotEmpty(basePageResponseList)){
                    redisUtil.set(key,JSONUtil.toJsonStr(basePageResponseList), 24* 60 * 60);
                }
                pageResponseList.addAll(basePageResponseList);
            }

            if(CollectionUtil.isNotEmpty(pageResponseList)){
                //品牌墙产品图片筛选
                List<PageResponse> brandWallPageResponseList = pageResponseList.stream().filter(pageResponse -> pageResponse.getModuleCode().equals(ModuleCodeEnum.BRANDS.getCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(brandWallPageResponseList)){
                    brandWallPageResponseList.stream().forEach(
                            pageResponse -> {
                                List<ModuleDetailResponse> brandWallModuleDetailList = pageResponse.getModuleDetails();
                                if (CollectionUtil.isNotEmpty(brandWallModuleDetailList)){
                                    if (brandWallModuleDetailList.size() > 10){
                                        brandWallModuleDetailList.subList(0, 10).stream().forEach(moduleDetail -> moduleDetail.setImgUrl(moduleDetail.getProductImgUrl()));
                                        brandWallModuleDetailList.subList(10, brandWallModuleDetailList.size()).stream().forEach(moduleDetailEntity -> {
                                            moduleDetailEntity.setImgUrl(moduleDetailEntity.getProductLogoUrl());
                                            if (StringUtils.isNotEmpty(moduleDetailEntity.getBrandName())){
                                                moduleDetailEntity.setProductName(moduleDetailEntity.getBrandName());
                                            }
                                        });
                                    }else {
                                        brandWallModuleDetailList.stream().forEach(moduleDetail -> moduleDetail.setImgUrl(moduleDetail.getProductImgUrl()));
                                    }
                                }
                            }
                    );
                }
                //风控筛选
                try {
                    Long customerId = StpUtil.getLoginIdAsLong();
                    List<SpuRiskFilterEntity> riskDisableProducts = productRiskControlService.getRiskDisableProducts(customerId);
                    if (CollectionUtil.isNotEmpty(riskDisableProducts)){
                        riskDisableProducts.forEach(r -> r.setProductType(r.getProductType()*100));  //统一将type乘100，和moduledetail中type一致
                        pageResponseList.removeIf(p -> shouldRemovePageResponse(p,riskDisableProducts));
                    }
                }catch (Exception e){
                    log.error("风控筛选失败:{}", e.getMessage());

                }
            }
            //获取全球购和本地生活tab是否显示
            Integer outGoodsMinNum = 10;
            try {
                String outGoodsMinNumStr = configRepository.getValueByType(SystemConfigTypeEnum.OUT_GOODS_MIN_NUM);
                outGoodsMinNum = Integer.parseInt(outGoodsMinNumStr);
            }catch (Exception e){
                log.error("获取全局购和本地生活是否显示的商品数阈值失败:{}", e.getMessage());
            }
            List<PageResponse> tabPageResponseList = pageResponseList.stream()
                    .filter(pageResponse -> pageResponse.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tabPageResponseList)){
                PageResponse tabPageResponse = tabPageResponseList.get(0);
                List<ModuleDetailResponse> localGlobalTabList = new ArrayList<>();
                try {
                    //全球购和本地生活tab组件
                    if (cityId != null && !cityId.equals(0)){
                        //本地生活只使用市查询
                        RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                        if (regionEntity != null){
                            String cityName = regionEntity.getRegionName().substring(0,2);
                            String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                            Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                            if (CollectionUtil.isNotEmpty(stationSet)){
                                Optional<String> firstElement = stationSet.stream().findFirst();
                                String stationKey = firstElement.get();
                                String stationValue = redisUtil.get(stationKey);
                                ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                Integer productNum = moduleLocalLifeInfoEntity.getProductNum();
                                if (productNum != null && productNum > outGoodsMinNum){
                                    ModuleDetailResponse localDetailResponse = new ModuleDetailResponse();
                                    localDetailResponse.setModuleId(tabPageResponse.getModuleId());
                                    localDetailResponse.setParentDetailId(0);
                                    localDetailResponse.setDetailType(ModuleDetailType.SON_LOCAL_LIFE_TAB.getCode());
                                    localGlobalTabList.add(localDetailResponse);
                                }
                            }
                        }
                    }

                }catch (Exception e){
                    log.error("获取本地生活tab失败:{}",e.getMessage());
                }
                try {
                    //获取全球购商品数量
                    String globalKey = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
                    if (redisUtil.zCard(globalKey).compareTo(Long.valueOf(outGoodsMinNum)) > 0){
                        ModuleDetailResponse globalDetailResponse = new ModuleDetailResponse();
                        globalDetailResponse.setModuleId(tabPageResponse.getModuleId());
                        globalDetailResponse.setParentDetailId(0);
                        globalDetailResponse.setDetailType(ModuleDetailType.SON_GLOBAL_TAB.getCode());
                        localGlobalTabList.add(globalDetailResponse);
                    }
                }catch (Exception e){
                    log.error("获取全球购tab失败:{}",e.getMessage());
                }
                List<ModuleDetailResponse> tabDetailResponseList = tabPageResponse.getModuleDetails();
                if(CollectionUtil.isNotEmpty(tabDetailResponseList)){
                    tabDetailResponseList.addAll(localGlobalTabList);
                }else {
                    tabPageResponse.setModuleDetails(localGlobalTabList);
                }
            }
            return pageResponseList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<PageResponse> getPageInfo(Integer pageRegionId) {
        List<ModuleRegionRelationalEntity> moduleRegionRelationalEntityList = moduleRegionRelationalRepository.getModuleRegionRelational(pageRegionId);
        if (CollectionUtil.isNotEmpty(moduleRegionRelationalEntityList)){
            PageRegionEntity pageRegionEntity = pageRegionRepository.getById(pageRegionId);
            if (pageRegionEntity == null){
                log.error("获取页面区域异常");
                return Collections.emptyList();
            }
            List<Integer> moduleIds = moduleRegionRelationalEntityList.stream()
                    .map(ModuleRegionRelationalEntity::getModuleId).toList();
            List<ModuleInfoEntity> moduleInfoEntityList = moduleInfoRepository.getModuleListByIds(moduleIds);
            if (CollectionUtil.isNotEmpty(moduleInfoEntityList)){
                //筛选出不包含tab的组件
                List<Integer> filterModuleIds = moduleInfoEntityList.stream().filter(moduleInfoEntity -> !moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                        .map(ModuleInfoEntity::getModuleId).collect(Collectors.toList());
                Map<Integer, String> moduleCodeMap = moduleInfoEntityList.stream().collect(Collectors.toMap(ModuleInfoEntity::getModuleId, ModuleInfoEntity::getModuleCode));
                List<ModuleDetailEntity> moduleDetailEntityList = moduleDetailRepository.getAllModuleDetailListByModuleIds(filterModuleIds,pageRegionEntity.getCityId(),pageRegionEntity.getDistrictId());
                Map<Integer, List<ModuleDetailEntity>> moduleDetailEntityMap = moduleDetailEntityList.stream().collect(Collectors.groupingBy(ModuleDetailEntity::getModuleId));
                List<PageResponse> pageResponseList = new ArrayList<>(moduleInfoEntityList.size());
                for (ModuleInfoEntity moduleInfoEntity : moduleInfoEntityList){
                    PageResponse pageResponse = new PageResponse();
                    if (moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode())){
                        //基础tab栏只保存sontab类型详情,单独接口获取详情列表
                        pageResponse.setModuleId(moduleInfoEntity.getModuleId());
                        pageResponse.setModuleCode(moduleInfoEntity.getModuleCode());
                        List<ModuleDetailEntity> tabDetailEntityList = moduleDetailRepository.getTabDetailListByModuleId(moduleInfoEntity.getModuleId());
                        if (CollectionUtil.isNotEmpty(tabDetailEntityList)){
                            List<ModuleDetailEntity> tabDetailRespEntityList = new ArrayList<>();
                            for (ModuleDetailEntity sonTabDetailEntity : tabDetailEntityList){
                                //获取基础tab栏详情是否可售
                                TabPageRequest tabPageRequest = new TabPageRequest();
                                tabPageRequest.setModuleDetailId(sonTabDetailEntity.getDetailId());
                                if (Objects.equals(pageRegionEntity.getDistrictId(), 0)){
                                    tabPageRequest.setRegionId(pageRegionEntity.getCityId());
                                }else {
                                    tabPageRequest.setRegionId(pageRegionEntity.getDistrictId());
                                }
                                tabPageRequest.setActivityId(pageRegionEntity.getActivityId());
                                PageDTO<ModuleDetailResponse> tabPageResponse = this.tabPage(tabPageRequest);
                                if (CollectionUtil.isNotEmpty(tabPageResponse.getRecords())){
                                    tabDetailRespEntityList.add(sonTabDetailEntity);
                                }
                            }
                            if (CollectionUtil.isNotEmpty(tabDetailRespEntityList)){
                                pageResponse.setModuleDetails(BeanUtil.copyToList(tabDetailRespEntityList, ModuleDetailResponse.class));
                            }
                        }
                        pageResponseList.add(pageResponse);
                        continue;
                    }
                    BeanUtil.copyProperties(moduleInfoEntity,pageResponse);
                    List<ModuleDetailEntity> onSalemoduleDetailList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                        //组装组件详情
                        List<ModuleDetailEntity> moduleDetailList = moduleDetailEntityMap.get(moduleInfoEntity.getModuleId());
                        if (CollectionUtil.isNotEmpty(moduleDetailList)){
                            //使用组件详情策略生成图片等内容(针对卡券，应用等)
                            updateModuleDetailEntityListInfo(moduleDetailList);
                            this.getAllOnSaleModuleDetailList(moduleDetailList, onSalemoduleDetailList); //筛选出所有可售商品
                            if(CollectionUtil.isNotEmpty(onSalemoduleDetailList)){
                                onSalemoduleDetailList.sort(Comparator.comparing(ModuleDetailEntity::getSortOrder));
                                List<ModuleDetailResponse> moduleDetailResponseList = this.deepCopyToResponse(onSalemoduleDetailList, false);
                                pageResponse.setModuleDetails(moduleDetailResponseList);
                            }
                        }
                    }
                    if(!ModuleCodeEnum.BASIC_CONFIG.getCode().equals(moduleInfoEntity.getModuleCode())){
                        //非基础组件，若详情为空，则不显示
                        if (CollectionUtil.isEmpty(onSalemoduleDetailList)){
                            continue;
                        }
                    }
                    pageResponseList.add(pageResponse);
                }
                return pageResponseList;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void updateAllPageRegionCache() {
        List<PageInfoEntity> allReleasedPage = pageInfoRepository.getAllNeedUpdatePage();
        if (CollectionUtil.isEmpty(allReleasedPage)){
            throw new BizException("没有获取到需要更新的页面");
        }
        List<Integer> pageIds = allReleasedPage.stream()
                .map(PageInfoEntity::getPageId).collect(Collectors.toList());
        List<PageRegionEntity> pageRegionEntityList = pageRegionRepository.getAllPageRegionByPageIds(pageIds);
        String pattern = "page:region:" + "*";
        Set<String> existKeyList = redisUtil.scan(pattern, 100);
        // 如果列表为空，删除所有以page:region:*开头的缓存
        if (pageRegionEntityList == null || pageRegionEntityList.isEmpty()) {
            deleteAllPageRegionRedisKeys(existKeyList);
            return;
        }
        Set<String> dbKeys = pageRegionEntityList.stream()
                .map(entity -> RedisKeyConstants.PAGE_REGION.formatted(entity.getActivityId(), entity.getPageId(), entity.getCityId(), entity.getDistrictId()))
                .collect(Collectors.toSet());
        //遍历当前缓存，删除不存在数据库中的缓存
        if (CollectionUtil.isNotEmpty(existKeyList)){
            for (String redisKey : existKeyList) {
                if (!dbKeys.contains(redisKey)) {
                    redisUtil.delete(redisKey);
                }
            }
        }
        //遍历数据库列表，插入不在Redis中的记录
        for (PageRegionEntity entity : pageRegionEntityList) {
            String redisKey = RedisKeyConstants.PAGE_REGION.formatted(entity.getActivityId(), entity.getPageId(), entity.getCityId(), entity.getDistrictId());
            if (existKeyList == null || !existKeyList.contains(redisKey)) {
                redisUtil.set(redisKey, String.valueOf(entity.getPageRegionId()), true);
            }
        }
    }

    @Override
    public void updateAllPageCache() {
        String pattern = "page:region:" + "*";
        Set<String> existPageRegionKeyList = redisUtil.scan(pattern, 100);
        if (CollectionUtil.isNotEmpty(existPageRegionKeyList)){
            for (String pageRegionKey : existPageRegionKeyList) {
                try {
                    Integer pageRegionId = Integer.valueOf(redisUtil.get(pageRegionKey));
                    List<PageResponse> pageResponseList = this.getPageInfo(pageRegionId);
                    if (CollectionUtil.isNotEmpty(pageResponseList)){
                        String pageKey = RedisKeyConstants.PAGE_INFO + pageRegionId;
                        redisUtil.set(pageKey,JSONUtil.toJsonStr(pageResponseList), 24* 60 * 60);
                    }
                }catch (Exception e){
                    log.error("更新页面缓存异常:{}", e.getMessage());
                }

            }
        }
    }

    @Override
    public void updateAllGlobalGoodsCache() {
        GlobalGoodsListRequest request = new GlobalGoodsListRequest();
        request.setKeyword("RX");
        List<GlobalGoodsListResponse> globalGoodsList = globalGateWay.getGoodsList(request);
        if (globalGoodsList == null){
            return;
        }
        String key = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
        redisUtil.delete(key);
        if (CollectionUtil.isNotEmpty(globalGoodsList)){
            int i = 0;
            Map<String, Double> scoreMembers = new HashMap<>();
            for (GlobalGoodsListResponse globalGoodsListResponse : globalGoodsList){
                ModuleDetailEntity moduleDetailEntity = new ModuleDetailEntity();
                moduleDetailEntity.setProductId(globalGoodsListResponse.getProductId());
                moduleDetailEntity.setProductItemId(globalGoodsListResponse.getProductItemId());
                moduleDetailEntity.setImgUrl(globalGoodsListResponse.getImgUrl());
                moduleDetailEntity.setProductImgUrl(globalGoodsListResponse.getImgUrl());
                moduleDetailEntity.setProductName(globalGoodsListResponse.getProductName());
                moduleDetailEntity.setPromotionSubscriptImg(globalGoodsListResponse.getActivityImgUrl());
                moduleDetailEntity.setLabelCopy(globalGoodsListResponse.getActivityLabel());
                moduleDetailEntity.setDetailType(ModuleDetailType.GLOBAL_GOODS.getCode());
                moduleDetailEntity.setProductType(ModuleDetailType.GLOBAL_GOODS.getCode());
                moduleDetailEntity.setPromotionLabels(globalGoodsListResponse.getPromotionLabels());
                moduleDetailEntity.setProductPrice(globalGoodsListResponse.getPrice() != null? globalGoodsListResponse.getPrice().toString() : null);
                moduleDetailEntity.setProductOriginalPrice(globalGoodsListResponse.getOriginalPrice() != null? globalGoodsListResponse.getOriginalPrice().toString() : null);
                scoreMembers.put(JSONObject.toJSONString(moduleDetailEntity), Double.valueOf(i));
                i += 1;
            }
            redisUtil.zadd(key, scoreMembers);
        }
    }

    @Override
    public void updateAllLocalLifeGoodsCache() {
        //获取所有站点
        List<SubstationResponse> substationResponseList = localLifeGateWay.getSubstationList("");
        if (substationResponseList == null){
            return;
        }
        String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + "*";
        //删除所有站点信息的缓存(后续重新生成)
        Set<String> existStationList = redisUtil.scan(stationPattern, 100);
        if(CollectionUtil.isEmpty(substationResponseList)){
            if (CollectionUtil.isNotEmpty(existStationList)){
                for (String redisKey : existStationList) {
                    redisUtil.delete(redisKey);
                }
            }
            redisUtil.delete(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE);
            return;
        }
        Set<String> siteKeySet = substationResponseList.stream()
                .map(response -> RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + response.getSiteAreaName())
                .collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(existStationList)){
            for (String key : existStationList) {
                if (!siteKeySet.contains(key)) {
                    String stationValue = redisUtil.get(key);
                    if (stationValue == null){
                        redisUtil.delete(key);
                        ModuleLocalLifeInfoEntity station = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                        String fieldKey = station.getSiteId() + ":" + 10;
                        String allFieldKey = station.getSiteId() + ":" + 50;
                        redisUtil.delete(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, fieldKey);
                        redisUtil.delete(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, allFieldKey);
                    }
                }
            }
        }
        for (SubstationResponse substationResponse : substationResponseList){
            ActivityListRequest request = new ActivityListRequest();
            request.setWebSiteId(substationResponse.getSiteId());
            request.setLabelType(10); //热门推荐
            request.setPage("1");
            request.setPageSize("200");
            List<ModuleDetailEntity> moduleDetailEntityList = getAllModuleLocalLifeResponse(request);
            if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                String fieldKey = substationResponse.getSiteId() + ":" + 10;   //使用站点id和标签类型作为字段名
                redisUtil.hset(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, fieldKey, JSONArray.toJSONString(moduleDetailEntityList));
            }
            request.setLabelType(50); //全部
            moduleDetailEntityList = getAllModuleLocalLifeResponse(request);
            if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                String fieldKey = substationResponse.getSiteId() + ":" + 50;
                redisUtil.hset(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, fieldKey, JSONArray.toJSONString(moduleDetailEntityList));
            }
            String stationKey = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + substationResponse.getSiteAreaName();
            ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = new ModuleLocalLifeInfoEntity();
            moduleLocalLifeInfoEntity.setSiteId(substationResponse.getSiteId());
            moduleLocalLifeInfoEntity.setSiteAreaName(substationResponse.getSiteAreaName());
            moduleLocalLifeInfoEntity.setProductNum(CollectionUtil.isNotEmpty(moduleDetailEntityList)? moduleDetailEntityList.size():0);
            redisUtil.set(stationKey, JSONObject.toJSONString(moduleLocalLifeInfoEntity), 24* 60 * 60);
        }
    }

    @Override
    public void updateAllTabCache() {
        String pattern = "page:region:" + "*";
        Set<String> existPageRegionKeyList = redisUtil.scan(pattern, 100);
        if (CollectionUtil.isNotEmpty(existPageRegionKeyList)){
            for (String pageRegionKey : existPageRegionKeyList) {
                Integer pageRegionId = Integer.valueOf(redisUtil.get(pageRegionKey));
                List<ModuleRegionRelationalEntity> moduleRegionRelationalEntityList = moduleRegionRelationalRepository.getModuleRegionRelational(pageRegionId);
                if (CollectionUtil.isNotEmpty(moduleRegionRelationalEntityList)){
                    List<Integer> moduleIds = moduleRegionRelationalEntityList.stream().map(ModuleRegionRelationalEntity::getModuleId).collect(Collectors.toList());
                    List<ModuleInfoEntity> moduleInfoEntityList = moduleInfoRepository.getModuleListByIds(moduleIds);
                    List<ModuleInfoEntity> tabModuleInfoEntityList = moduleInfoEntityList.stream().filter(moduleInfoEntity -> moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(tabModuleInfoEntityList)){
                        //获取tab组件下detailtype为sontab的详情列表
                        tabModuleInfoEntityList.forEach(moduleInfoEntity ->{
                            try {
                                List<ModuleDetailEntity> moduleDetailEntityList = moduleDetailRepository.getTabDetailListByModuleId(moduleInfoEntity.getModuleId());
                                if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                                    moduleDetailEntityList.forEach(moduleDetailEntity ->{
                                        List<ModuleDetailResponse> detailResponseList = this.getTabInfo(pageRegionId, moduleDetailEntity.getDetailId());
                                        String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, moduleDetailEntity.getDetailId());
                                        this.addTabCache(detailResponseList, pageTabKey);
                                        log.info("updateAllTabCache 添加tab缓存, key={}", pageTabKey);
                                    });
                                }
                            }catch (Exception e){
                                log.error("更新tab缓存异常moduleId:{}, exception{}", moduleInfoEntity.getModuleId(), e.getMessage());
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    public PageDTO<ModuleDetailResponse> tabPage(TabPageRequest request) {
        Integer activityId = request.getActivityId();
        Integer regionId = request.getRegionId();
        Integer moduleDetailId = request.getModuleDetailId();
        Integer detailType = request.getDetailType();
        Integer pageRows = request.getPageRows();
        Integer toPage = request.getToPage();
        Integer pageId = 0;
        if (activityId.equals(0)){
            //首页需要获取当前发布页
            PageInfoEntity  pageInfoEntity = pageInfoRepository.getCurrentHomePage();
            pageId = pageInfoEntity.getPageId();
        }else {
            //活动页pageId=activityId
            pageId = activityId;
        }
        Integer cityId = 0;
        Integer districtId = 0;
        RegionEntity selectRegionEntity = regionRepository.getRegionById(regionId);
        if (selectRegionEntity != null){
            if (selectRegionEntity.getRegionType().equals(2)){
                cityId = selectRegionEntity.getId();
            }else if (selectRegionEntity.getRegionType().equals(3)) {
                cityId = selectRegionEntity.getParentId();
                districtId = selectRegionEntity.getId();
            }else {
                log.error("区域类型错误,使用全国页面 regionId = {}", regionId);
            }
        }else {
            log.error("区域类型错误,使用全国页面 regionId = {}", regionId);
        }

        Double lat = null;
        Double lng = null;
        Long customerId = null;
        try {
            customerId = StpUtil.getLoginIdAsLong();
            RegionEntity userRegionEntity = regionRepository.getCurrentRegion(customerId);
            lat = Double.parseDouble(userRegionEntity.getLat());
            lng = Double.parseDouble(userRegionEntity.getLng());
        }catch (Exception e){
            //未登录，使用前端传入的经纬度
            if (StrUtil.isNotEmpty(request.getLat()) && StrUtil.isNotEmpty(request.getLng())){
                lat = Double.parseDouble(request.getLat());
                lng = Double.parseDouble(request.getLng());
            }
        }
        //根据区域获取page_region
        PageRegionEntity pageRegionEntity = pageRegionRepository.getPageRegion(activityId, pageId, cityId, districtId);
        if (pageRegionEntity != null && pageRegionEntity.getPageRegionId() != null) {
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            Long total = 0l;
            List<ModuleDetailResponse> detailResponseList = new ArrayList<>();
            if (moduleDetailId != null){
                String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, moduleDetailId);
                if(redisUtil.exists(pageTabKey)){
                    total = redisUtil.zCard(pageTabKey);
                    Set<String> detailResponseSet = redisUtil.zrange(pageTabKey, toPage * pageRows - pageRows, toPage * pageRows - 1);
                    if (CollectionUtil.isNotEmpty(detailResponseSet)){
                        detailResponseList = detailResponseSet.stream()
                                .map(detailResponse -> JSONObject.parseObject(detailResponse, ModuleDetailResponse.class))
                                .collect(Collectors.toList());
                    }
                }else {
                    List<ModuleDetailResponse> moduleDetailEntityList = getTabInfo(pageRegionId, moduleDetailId);
                    if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                        this.addTabCache(moduleDetailEntityList, pageTabKey);
                        total = Long.valueOf(moduleDetailEntityList.size());
                        detailResponseList = moduleDetailEntityList.stream().skip(toPage * pageRows - pageRows).limit(pageRows).collect(Collectors.toList());
                    }
                }
                if(CollectionUtil.isNotEmpty(detailResponseList)){
                    this.goodsDistance(detailResponseList, lat, lng);
                }
                //风控筛选
                if (customerId != null){
                    try {
                        List<SpuRiskFilterEntity> riskDisableProducts = productRiskControlService.getRiskDisableProducts(customerId);
                        if (CollectionUtil.isNotEmpty(riskDisableProducts)){
                            riskDisableProducts.forEach(r -> r.setProductType(r.getProductType()*100));  //统一将type乘100，和moduledetail中type一致
                            //根据风控判断是否删除当前模块详情
                            Set<String> riskDisableProductsSet = riskDisableProducts.stream().map(
                                    r ->{
                                        return String.valueOf(r.getProductType()) + "|" + String.valueOf(r.getProductSpuId());
                                    }
                            ).collect(Collectors.toSet());
                            this.removeModuleDetailRecursively(detailResponseList, riskDisableProductsSet);
                        }
                    }catch (Exception e){
                        log.error("风控筛选失败:{}", e.getMessage());
                    }
                }

            }else if (detailType != null){
                //全球购or本地生活tab(全球购和本地生活缓存中存储是ModuleDetailEntity列表,需要转成ModuleDetailResponse)
                if(detailType.equals(ModuleDetailType.SON_GLOBAL_TAB.getCode())){
                    String globalKey = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
                    total = redisUtil.zCard(globalKey);
                    Set<String> detailResponseSet = redisUtil.zrange(globalKey, toPage * pageRows - pageRows, toPage * pageRows - 1);
                    if (CollectionUtil.isNotEmpty(detailResponseSet)){
                        List<ModuleDetailEntity> detailEntityList = detailResponseSet.stream()
                                .map(detailEntity -> JSONObject.parseObject(detailEntity, ModuleDetailEntity.class))
                                .collect(Collectors.toList());
                        detailResponseList = this.deepCopyToResponse(detailEntityList,true);
                    }
                }else if (detailType.equals(ModuleDetailType.SON_LOCAL_LIFE_TAB.getCode())){
                    if (cityId != null && !cityId.equals(0)){
                        //本地生活只使用城市id获取
                        RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                        if (regionEntity != null){
                            String cityName = regionEntity.getRegionName().substring(0,2);
                            String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                            Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                            if (CollectionUtil.isNotEmpty(stationSet)){
                                Optional<String> firstElement = stationSet.stream().findFirst();
                                String stationKey = firstElement.get();
                                String stationValue = redisUtil.get(stationKey);
                                ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                Long siteId = moduleLocalLifeInfoEntity.getSiteId();
                                if (siteId != null){
                                    String field = siteId + ":" + 50; //全部
                                    String globalValue = redisUtil.hget(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, field);
                                    if (StringUtils.isNotEmpty(globalValue)){
                                        List<ModuleDetailEntity> localModuleDetailEntityList = JSONArray.parseArray(globalValue, ModuleDetailEntity.class);
                                        total = Long.valueOf(localModuleDetailEntityList.size());
                                        List<ModuleDetailEntity> detailEntityList = localModuleDetailEntityList.stream().skip(toPage * pageRows - pageRows).limit(pageRows).collect(Collectors.toList());
                                        detailResponseList = this.deepCopyToResponse(detailEntityList, true);
                                        if(CollectionUtil.isNotEmpty(detailResponseList)){
                                            this.goodsDistance(detailResponseList, lat, lng);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return PageDTO.build(detailResponseList, total,  Long.valueOf(pageRows) , Long.valueOf(toPage));
        }
        return PageDTO.build(null, 0l,  Long.valueOf(pageRows), Long.valueOf(toPage));
    }


    @Override
    public void handleReleasePage(Integer pageId, Integer activityId) {
        //更新页面区域缓存
        this.updateAllPageRegionCache();
        List<PageRegionEntity> pageRegionEntityList = pageRegionRepository.getAllPageRegionByPageId(pageId, activityId);
        if (CollectionUtil.isEmpty(pageRegionEntityList)){
            log.error("未获取到页面区域 pageId = {}", pageId);
        }
        pageRegionEntityList.forEach(pageRegionEntity -> {
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            String key = RedisKeyConstants.PAGE_INFO + pageRegionId;
            redisUtil.delete(key);
            List<PageResponse> basePageResponseList = this.getPageInfo(pageRegionId);
            if (CollectionUtil.isNotEmpty(basePageResponseList)){
                redisUtil.set(key,JSONUtil.toJsonStr(basePageResponseList), 24* 60 * 60);
                basePageResponseList.stream().filter(pageResponse -> pageResponse.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                        .forEach(pageResponse -> {
                            if (pageResponse.getModuleDetails() != null){
                                pageResponse.getModuleDetails().stream().filter(moduleDetailResponse -> moduleDetailResponse.getDetailType().equals(ModuleDetailType.SON_TAB.getCode()))
                                        .forEach(moduleDetailResponse -> {
                                            Integer detailId = moduleDetailResponse.getDetailId();
                                            List<ModuleDetailResponse> detailResponseList = this.getTabInfo(pageRegionId, detailId);
                                            String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, detailId);
                                            this.addTabCache(detailResponseList, pageTabKey);
                                            log.info("handleReleasePage 添加tab缓存, key={}", pageTabKey);
                                        });
                            }
                        });
            }
        });
    }

    /**
     * 普通son tab(区分全球购和本地生活tab)内容
     * @param pageRegionId
     * @param moduleDetailId
     * @return {@link List}<{@link ModuleDetailResponse}>
     */
    public List<ModuleDetailResponse> getTabInfo(Integer pageRegionId, Integer moduleDetailId) {
        PageRegionEntity pageRegionEntity = pageRegionRepository.getById(pageRegionId);
        if (pageRegionEntity == null){
            log.error("获取页面区域异常");
            return null;
        }
        ModuleDetailEntity tabModuleDetailEntity = moduleDetailRepository.getById(moduleDetailId);
        if (tabModuleDetailEntity == null){
            log.error("未获取tab组件详情, moduleDetailId={}", moduleDetailId);
            return null;
        }
        List<ModuleDetailEntity> allModuleDetailEntityList = moduleDetailRepository.getAllModuleDetailListByModuleIds(Arrays.asList(tabModuleDetailEntity.getModuleId()), pageRegionEntity.getCityId(), pageRegionEntity.getDistrictId());
        if (CollectionUtil.isEmpty(allModuleDetailEntityList)) {
            log.error("未获取tab组件, moduleDetailId={}", moduleDetailId);
            return null;
        }
        tabModuleDetailEntity = allModuleDetailEntityList.stream()
                .filter(moduleDetail -> moduleDetail.getDetailId().equals(moduleDetailId))
                .findFirst().get();
        if (tabModuleDetailEntity != null && tabModuleDetailEntity.getDetailType().equals(ModuleDetailType.SON_TAB.getCode())) {
            if (CollectionUtil.isNotEmpty(allModuleDetailEntityList)) {
                List<ModuleDetailEntity> moduleDetailEntityList = tabModuleDetailEntity.getChildren();
                if (CollectionUtil.isEmpty(moduleDetailEntityList)){
                    log.error("未获取tab组件详情列表, moduleDetailId={}", moduleDetailId);
                    return null;
                }
                this.updateModuleDetailEntityListInfo(moduleDetailEntityList);
                List<ModuleDetailEntity> onSaleModuleDetailList = new ArrayList<>();
                this.getAllOnSaleModuleDetailList(moduleDetailEntityList, onSaleModuleDetailList); //筛选出所有可售商品
                if (CollectionUtil.isNotEmpty(onSaleModuleDetailList)){
                    //组装本地生活
                    List<ModuleDetailEntity> localModuleDetailList = new ArrayList<>();
                    try {
                        //本地生活tab组件
                        Integer cityId = pageRegionEntity.getCityId();
                        if (cityId != null && !cityId.equals(0)){
                            //本地生活只使用市查询
                            RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                            if (regionEntity != null){
                                String cityName = regionEntity.getRegionName().substring(0,2);
                                String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                                Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                                if (CollectionUtil.isNotEmpty(stationSet)){
                                    Optional<String> firstElement = stationSet.stream().findFirst();
                                    String stationKey = firstElement.get();
                                    String stationValue = redisUtil.get(stationKey);
                                    ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                    Long siteId = moduleLocalLifeInfoEntity.getSiteId();
                                    if (siteId != null){
                                        String field = siteId + ":" + 10; //推荐
                                        String globalValue = redisUtil.hget(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, field);
                                        if (StringUtils.isNotEmpty(globalValue)){
                                            localModuleDetailList = JSONArray.parseArray(globalValue, ModuleDetailEntity.class);
                                        }
                                    }
                                }
                            }
                        }
                    }catch (Exception e){
                        log.error("获取本地生活tab失败:{}",e.getMessage());
                    }
                    int localIndex = 0;
                    List<ModuleDetailEntity> tabModuleDetailList = new ArrayList<>();
                    for (ModuleDetailEntity moduleDetailEntity : onSaleModuleDetailList) {
                        if (moduleDetailEntity.getDetailType().equals(ModuleDetailType.LOCAL_LIFE.getCode())){
                            if (localIndex >= localModuleDetailList.size()){
                                continue;
                            }
                            ModuleDetailEntity localModuleDetailEntity = localModuleDetailList.get(localIndex);
                            localModuleDetailEntity.setDetailId(moduleDetailEntity.getDetailId());
                            tabModuleDetailList.add(localModuleDetailEntity);
                            localIndex += 1;
                        }else {
                            tabModuleDetailList.add(moduleDetailEntity);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(tabModuleDetailList)){
                        return this.deepCopyToResponse(tabModuleDetailList, true);
                    }
                }
            }
        }
        return null;
    }

    private void addTabCache(List<ModuleDetailResponse> moduleDetailEntityList, String key){
        if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
            Map<String, Double> scoreMembers = new HashMap<>();
            for (int i = 0; i < moduleDetailEntityList.size(); i++){
                scoreMembers.put(JSONObject.toJSONString(moduleDetailEntityList.get(i)), Double.valueOf(i));
            }
            redisUtil.delete(key);
            redisUtil.zadd(key,scoreMembers);
            redisUtil.expire(key, 24* 60 * 60);
            log.info("添加tab缓存, key={}", key);
        }
    }

    private void updateModuleDetailEntityListInfo(List<ModuleDetailEntity> entities){
        if (CollectionUtil.isEmpty(entities)) {
            return;
        }
        entities.forEach(entity -> updateEntityInfo(entity));
    }

    private void updateEntityInfo(ModuleDetailEntity entity){
        if (entity == null) {
            return;
        }
        ModuleDetailStrategy moduleDetailStrategy = moduleDetailFactory.getModuleDetailStrategy(ModuleDetailType.getByCode(entity.getDetailType()));
        if (moduleDetailStrategy != null){
            moduleDetailStrategy.updateModuleDetailEntityInfo(entity);
        }
        // 递归更新子节点
        updateModuleDetailEntityListInfo(entity.getChildren());
        if (CollectionUtil.isNotEmpty(entity.getChildren())){
            //子节点中无上架商品，则父节点不上架,并更新父节点状态
            if (entity.getChildren().stream().filter(child -> child.getOnSale().equals(true)).count() == 0){
                entity.setOnSale(false);
                if (moduleDetailStrategy == null){
                    moduleDetailStrategy = moduleDetailFactory.getModuleDetailStrategy(ModuleDetailType.COMMON);
                }
                moduleDetailStrategy.updateModuleDetailEntityStatusOrDel(entity.getDetailId(), 0, 0);
            }
        }
    }

    // 将 List<ModuleDetailEntity> 深拷贝为 List<ModuleDetailResponse>
    private List<ModuleDetailResponse> deepCopyToResponse(List<ModuleDetailEntity> entities, boolean isTab) {
        if (CollectionUtil.isEmpty(entities)) {
            return null;
        }
        return entities.stream()
                .map(x-> entityToResponse(x, isTab))
                .collect(Collectors.toList());
    }

    // 将单个 ModuleDetailEntity 转换为 ModuleDetailResponse
    private ModuleDetailResponse entityToResponse(ModuleDetailEntity entity, boolean isTab) {
        if (entity == null) {
            return null;
        }
        ModuleDetailResponse response = new ModuleDetailResponse();
        response.setDetailId(entity.getDetailId());
        response.setModuleId(entity.getModuleId());
        response.setParentDetailId(entity.getParentDetailId());
        response.setDetailTitle(entity.getDetailTitle());
        response.setDetailSubTitle(entity.getDetailSubTitle());
        response.setDetailType(entity.getDetailType());
        response.setProductId(entity.getProductId());
        response.setProductItemId(entity.getProductItemId());
        response.setProductOriginalPrice(entity.getProductOriginalPrice());
        response.setProductType(entity.getProductType());
        response.setImgUrl(entity.getImgUrl());
        response.setProductName(entity.getProductName());
        response.setBrandName(entity.getBrandName());
        response.setProductSubName(entity.getProductSubName());
        response.setProductPrice(entity.getProductPrice());
        if (isTab){
            if (StringUtils.isNotBlank(entity.getProductImgUrl()) && StringUtils.isBlank(entity.getImgUrl())){
                response.setImgUrl(entity.getProductImgUrl());
            }
            if (StringUtils.isNotBlank(entity.getProductOriginalName())){
                response.setProductName(entity.getProductOriginalName());
            }
            if (StringUtils.isNotBlank(entity.getProductOriginalSubName())){
                response.setProductSubName(entity.getProductOriginalSubName());
            }
        }
        response.setProductImgUrl(entity.getProductImgUrl());
        response.setProductLogoUrl(entity.getProductLogoUrl());
        response.setContent(entity.getContent());
        response.setSortOrder(entity.getSortOrder());
        response.setSubscriptImg(entity.getSubscriptImg());
        response.setPromotionSubscriptImg(entity.getPromotionSubscriptImg());
        response.setMiniAppId(entity.getMiniAppId());
        response.setDistance(entity.getDistance());
        response.setGhId(entity.getGhId());
        response.setLabelCopy(entity.getLabelCopy());
        response.setPromotionLabels(entity.getPromotionLabels());
        response.setAvailableTimeStr(entity.getAvailableTimeStr());
        response.setDiscountRate(entity.getDiscountRate());
        response.setLat(entity.getLat());
        response.setLng(entity.getLng());
        response.setAppFlag(entity.getAppFlag());
        // 递归拷贝子节点
        List<ModuleDetailResponse> childrenResponses = deepCopyToResponse(entity.getChildren(), isTab);
        response.setChildren(childrenResponses != null ? childrenResponses : new ArrayList<>());

        return response;
    }

    private void deleteAllPageRegionRedisKeys(Set<String> existKeyList) {
        if (CollectionUtil.isNotEmpty(existKeyList)){
            for (String redisKey : existKeyList) {
                redisUtil.delete(redisKey);
            }
        }
    }

    private List<ModuleDetailEntity> getAllModuleLocalLifeResponse(ActivityListRequest request){
        List<LocalLifeGoodsResponse> localLifeGoodsList =localLifeGateWay.getLocalActivities(request);
        if (CollectionUtil.isEmpty(localLifeGoodsList)){
            return Collections.emptyList();
        }
        List<ModuleDetailEntity> moduleDetailEntityList = new ArrayList<>();
        for (LocalLifeGoodsResponse localLifeGoodsResponse : localLifeGoodsList){
            ModuleDetailEntity moduleDetailEntity = new ModuleDetailEntity();
            moduleDetailEntity.setAppFlag("LOCALLIFE");
            if (localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.SHOP.getCode())){
                //商家
                moduleDetailEntity.setProductId(String.valueOf(localLifeGoodsResponse.getProductsId()));
                moduleDetailEntity.setProductItemId(String.valueOf(localLifeGoodsResponse.getProductsId()));
                moduleDetailEntity.setProductName(localLifeGoodsResponse.getProductsName());
                moduleDetailEntity.setProductType(ModuleDetailType.LOCAL_LIFE.getCode() + LocalLifeGoodsEnum.SHOP.getCode());
                moduleDetailEntity.setDetailType(ModuleDetailType.LOCAL_LIFE.getCode());
                if (localLifeGoodsResponse.getProductInfo() != null && CollectionUtil.isNotEmpty(localLifeGoodsResponse.getProductInfo().getGoodsList())){
                    LocalLifeProductInfoVO productInfo = localLifeGoodsResponse.getProductInfo();
                    List<String> promotionLabels = new ArrayList<>();
                    for (LocalLifeProductGoodsDetailVO localGoods : localLifeGoodsResponse.getProductInfo().getGoodsList()){
                        if (localGoods.getGoodsType() != null && localGoods.getGoodsType().equals(1)){
                            promotionLabels.add("券" + localGoods.getGoodsName());
                        }else {
                            promotionLabels.add("惠" + localGoods.getGoodsName());
                        }
                    }
                    moduleDetailEntity.setImgUrl(productInfo.getLogo());
                    moduleDetailEntity.setLat(StringUtils.isEmpty(productInfo.getLatitude())? null : productInfo.getLatitude());
                    moduleDetailEntity.setLng(StringUtils.isEmpty(productInfo.getLongitude())? null : productInfo.getLongitude());
                    moduleDetailEntity.setPromotionLabels(promotionLabels);
                }
                moduleDetailEntityList.add(moduleDetailEntity);
            }else if (localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.COUPON.getCode())
                    || localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.PACKAGE.getCode())){
                //券
                if (localLifeGoodsResponse.getProductInfo() == null){
                    continue;
                }
                LocalLifeProductInfoVO productInfo = localLifeGoodsResponse.getProductInfo();
                moduleDetailEntity.setProductId(String.valueOf(localLifeGoodsResponse.getProductsId()));
                moduleDetailEntity.setProductItemId(String.valueOf(productInfo.getSkuId()));
                moduleDetailEntity.setProductName(localLifeGoodsResponse.getProductsName());
                moduleDetailEntity.setProductType(ModuleDetailType.LOCAL_LIFE.getCode() +  localLifeGoodsResponse.getProductsType());
                moduleDetailEntity.setDetailType(ModuleDetailType.LOCAL_LIFE.getCode());
                moduleDetailEntity.setDiscountRate(productInfo.getDiscountRate());
                moduleDetailEntity.setProductPrice(productInfo.getPrice());
                moduleDetailEntity.setProductOriginalPrice(productInfo.getMarketPrice());
                moduleDetailEntity.setAvailableTimeStr(productInfo.getAvailableTimeStr());
                moduleDetailEntity.setImgUrl(productInfo.getGoodsImage());
                moduleDetailEntity.setLat(StringUtils.isEmpty(productInfo.getLatitude())? null : productInfo.getLatitude());
                moduleDetailEntity.setLng(StringUtils.isEmpty(productInfo.getLongitude())? null : productInfo.getLongitude());
                moduleDetailEntityList.add(moduleDetailEntity);
            }else {
                log.error("未知的本地生活商品类型:{}", localLifeGoodsResponse.getProductsType());
            }
        }
        return moduleDetailEntityList;
    }


    /**
     * 获取商品距离
     * @param detailResponseList
     * @param lat
     * @param lng
     */
    private void goodsDistance(List<ModuleDetailResponse> detailResponseList, Double lat, Double lng){
        try {
            if (lat == null || lng == null){
                return;
            }
            for (ModuleDetailResponse moduleDetailResponse : detailResponseList){
                if (moduleDetailResponse.getLat() != null && moduleDetailResponse.getLng() != null){
                    Double distance = GeoUtil.getDistance(lat, lng, Double.parseDouble(moduleDetailResponse.getLat()), Double.parseDouble(moduleDetailResponse.getLng()));
                    moduleDetailResponse.setDistance(distance.intValue());
                }
            }
        }catch (Exception e){
            log.error("更新商品距离异常:{}",e.getMessage());
        }
    }

    private boolean shouldRemovePageResponse(PageResponse pageResponse, List<SpuRiskFilterEntity> riskDisableProducts){
        if (CollectionUtil.isEmpty(riskDisableProducts)){
            return false;
        }
        if (pageResponse.getModuleCode() == null){
            return false;
        }
        //tab和基础组件不处理
        if (pageResponse.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()) || pageResponse.getModuleCode().equals(ModuleCodeEnum.BASIC_CONFIG.getCode())){
            return false;
        }
        List<ModuleDetailResponse> moduleDetailResponseList = pageResponse.getModuleDetails();
        if (CollectionUtil.isEmpty(moduleDetailResponseList)){
            return true;
        }
        //根据风控判断是否删除当前模块详情
        Set<String> riskDisableProductsSet = riskDisableProducts.stream().map(
                r ->{
                    return String.valueOf(r.getProductType()) + "|" + String.valueOf(r.getProductSpuId());
                }
        ).collect(Collectors.toSet());
        this.removeModuleDetailRecursively(moduleDetailResponseList, riskDisableProductsSet);
        if (CollectionUtil.isEmpty(moduleDetailResponseList)){
            return true;
        }
        return false;
    }

    /**
     * 根据风控判断是否删除当前模块详情
     * @param moduleDetailResponseList
     * @param riskDisableProducts
     */
    private void removeModuleDetailRecursively(List<ModuleDetailResponse> moduleDetailResponseList, Set<String> riskDisableProducts){
        if (CollectionUtil.isEmpty(moduleDetailResponseList)) {
            return;
        }
        Iterator<ModuleDetailResponse> iterator = moduleDetailResponseList.iterator();
        while (iterator.hasNext()) {
            ModuleDetailResponse moduleDetail = iterator.next();
            int childrenSize =moduleDetail.getChildren().size();
            // 递归处理子列表
            processSubDetailResponse(moduleDetail, riskDisableProducts);
            // 检查是否需要删除当前bean
            if (shouldRemoveModuleDetail(moduleDetail, riskDisableProducts)) {
                iterator.remove();
            }
            int newChildrenSize = moduleDetail.getChildren().size();
            if (childrenSize != newChildrenSize){
                iterator.remove();
            }
        }
    }

    private void processSubDetailResponse(ModuleDetailResponse subModuleDetailResponse, Set<String> riskDisableProducts) {
        if (CollectionUtil.isEmpty(subModuleDetailResponse.getChildren()))
            return;
        // 递归处理子列表
        removeModuleDetailRecursively(subModuleDetailResponse.getChildren(), riskDisableProducts);
    }

    private boolean shouldRemoveModuleDetail(ModuleDetailResponse moduleDetail, Set<String> riskDisableProducts) {
        if (CollectionUtil.isEmpty(riskDisableProducts))
            return false;
        if (moduleDetail.getProductId() == null || moduleDetail.getDetailType() == null)
            return false;
        Integer detailType = moduleDetail.getDetailType();
        Long productId = Long.parseLong(moduleDetail.getProductId());
        if (riskDisableProducts.contains(detailType + "|" + productId)){
            return true;
        }
        return false;
    }

    private void getAllOnSaleModuleDetailList(List<ModuleDetailEntity> moduleDetailEntityList, List<ModuleDetailEntity> onSaleModuleDetailList){
        if (CollectionUtil.isEmpty(moduleDetailEntityList)){
            return;
        }
        for (ModuleDetailEntity moduleDetailEntity : moduleDetailEntityList){
            ModuleDetailEntity onSaleModuleDetailEntity = new ModuleDetailEntity();
            BeanUtil.copyProperties(moduleDetailEntity, onSaleModuleDetailEntity);
            onSaleModuleDetailEntity.setModuleCode(ModuleCodeEnum.TAB.getCode());
            if (CollectionUtil.isNotEmpty(moduleDetailEntity.getChildren())){
                List<ModuleDetailEntity> onSaleChildModuleDetailList = new ArrayList<>(moduleDetailEntity.getChildren().size());
                getAllOnSaleModuleDetailList(moduleDetailEntity.getChildren(), onSaleChildModuleDetailList);
                if (CollectionUtil.isNotEmpty(onSaleChildModuleDetailList)){
                    onSaleModuleDetailEntity.setChildren(onSaleChildModuleDetailList);
                }
            }
            if (onSaleModuleDetailEntity.getOnSale()) {
                onSaleModuleDetailList.add(onSaleModuleDetailEntity);
            }

        }
    }
}
