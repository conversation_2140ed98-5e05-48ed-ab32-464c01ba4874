package com.jsrxjt.mobile.domain.product.service.factory.builder.impl;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductChannelEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductChannelRepository;
import com.jsrxjt.mobile.domain.product.service.factory.builder.ProductItemBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 应用类型ProductItem构建器
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/5/27
 */
@Component("appProductItemBuilder")
@RequiredArgsConstructor
@Slf4j
public class AppProductItemBuilder implements ProductItemBuilder {

    private final AppGoodsRepository appGoodsRepository;

    private final ProductBrandRepository productBrandRepository;
    private final ProductChannelRepository productChannelRepository;

    @Override
    public ProductItem build(ProductItemId productItemId) {
        Long spuId = productItemId.getSpuId();

        // 1. 查询应用商品信息
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(spuId);
        if (appGoodsEntity == null) {
            throw new IllegalArgumentException("未找到应用ID为 " + spuId + " 的应用商品信息");
        }

        // 2. 构建ProductItem
        return buildFromAppGoodsData(appGoodsEntity);
    }

    private ProductItem buildFromAppGoodsData(AppGoodsEntity appGoodsEntity) {
        ProductItem productItem = new ProductItem();

        // 设置标识
        productItem.setSpuId(appGoodsEntity.getAppId());
        productItem.setSkuId(0L); // 应用通常没有SKU
        productItem.setProductType(ProductTypeEnum.APP.getType()); // 应用类型
        productItem.setFlatProductType(300 + appGoodsEntity.getType()); // 扁平化类型：301普通应用 304扫码提货 305纳客宝

        // 设置商品基本信息
        productItem.setProductName(appGoodsEntity.getAppName());
        productItem.setProductLogo(appGoodsEntity.getLogoUrl());
        productItem.setImgUrl(appGoodsEntity.getImgUrl());
        productItem.setBrandId(appGoodsEntity.getBrandId());
        ProductBrandEntity brand = productBrandRepository.findById(appGoodsEntity.getBrandId());
        if (brand != null) {
            productItem.setBrandName(brand.getBrandName());
        }
        productItem.setChannelId(appGoodsEntity.getChannelId());
        ProductChannelEntity channel = productChannelRepository.findById(appGoodsEntity.getChannelId());
        if (channel != null) {
            productItem.setChannelName(channel.getChannelName());
        }
        productItem.setFirstCategoryId(appGoodsEntity.getFirstCatId());
        productItem.setCategoryId(appGoodsEntity.getSecondCatId());
        productItem.setStatus(appGoodsEntity.getStatus());
        productItem.setAppFlag(appGoodsEntity.getAppFlag());
        productItem.setPayType(appGoodsEntity.getPayType());
        productItem.setSourceTable("app_goods");

        // 应用类型的特殊处理
        // 应用类型通常没有库存概念，设置为无限库存
        productItem.setInventory(Integer.MAX_VALUE);
        productItem.setIsSyncPayment(appGoodsEntity.getIsSyncPayment());


        log.info("成功构建应用ProductItem: spuId={}, appFlag={}",
                appGoodsEntity.getAppId(), appGoodsEntity.getAppFlag());

        return productItem;
    }
}