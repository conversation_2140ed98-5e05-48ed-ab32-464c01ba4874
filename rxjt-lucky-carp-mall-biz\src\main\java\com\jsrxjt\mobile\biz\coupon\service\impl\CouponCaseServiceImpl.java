package com.jsrxjt.mobile.biz.coupon.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.coupon.dto.request.*;
import com.jsrxjt.mobile.api.coupon.dto.response.*;
import com.jsrxjt.mobile.api.coupon.types.CouponStatus;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.promotion.types.PromotionActivityTypeEnum;
import com.jsrxjt.mobile.api.ticket.response.BrandTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.response.TicketResponseDTO;
import com.jsrxjt.mobile.biz.coupon.service.CouponCaseService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.coupon.entity.CouponGoodsVideoEntity;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsRepository;
import com.jsrxjt.mobile.domain.coupon.repository.CouponGoodsSkuRepository;
import com.jsrxjt.mobile.domain.msg.entity.ProductOperatLogEntity;
import com.jsrxjt.mobile.domain.msg.enums.OperationLogTypeEnum;
import com.jsrxjt.mobile.domain.msg.repository.ProductOperaLogRepository;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageSubSkuRepository;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformCardCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformShopRequest;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformGateway;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformCardCodeResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformShopDataResponse;
import com.jsrxjt.mobile.domain.product.entity.ProductLableEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsValueEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductSpecsRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.promotion.service.PromotionService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SpuRiskFilterEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.jsrxjt.common.core.constant.RedisKeyConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class CouponCaseServiceImpl implements CouponCaseService {

    private final CouponGoodsRepository couponGoodsRepository;

    private final CouponGoodsSkuRepository couponGoodsSkuRepository;

    private final CouponPlatformFactory couponPlatformFactory;

    private final ProductSkuSellRegionService productSkuSellRegionService;
    private final PickPlatformGateway pickPlatformGateway;
    private final AdvertisementService advertisementService;
    private final ContentRegionService contentRegionService;
    private final ProductSpecsRepository productSpecsRepository;
    private final PackageGoodsRepository packageGoodsRepository;
    private final PackageGoodsSkuRepository packageGoodsSkuRepository;
    private final PackageSubSkuRepository packageSubSkuRepository;
    private final RedisUtil redisUtil;
    private final PromotionService promotionService;
    private final ProductOperaLogRepository productOperaLogRepository;
    private final RegionRepository regionRepository;
    private final TicketService ticketService;


    @Override
    public void updateAllCoupon() {
        List<ProductOperatLogEntity> logEntityList = new ArrayList<>();
        //获取卡管所有卡券列表
        Map<String, CouponGoodsSkuEntity> allCouponMap = getKgCouponMap();
        if (CollectionUtil.isEmpty(allCouponMap)) {
            log.error("卡管未查询到数据");
            return;
        }
        //变更卡券状态
        setCouponStatus(allCouponMap, logEntityList);
        //变更套餐状态
        setPackageStatus(allCouponMap, logEntityList);
        //插入日志
        productOperaLogRepository.insertAllBatch(logEntityList);
    }

    private void setPackageStatus(Map<String, CouponGoodsSkuEntity> allCouponMap, List<ProductOperatLogEntity> logEntityList) {
        List<PackageGoodsJobEntity> allPackageGoods = packageGoodsRepository.findAll();
        if (CollectionUtil.isEmpty(allPackageGoods)) {
            log.error("套餐未找到");
            return;
        }
        List<PackageSkuSubJobEntity> allPackageSkuSub = new ArrayList<>();//需要更新的子sku
        List<PackageGoodsSkuJobEntity> allPackageSku = new ArrayList<>();//需要更新的sku
        List<PackageGoodsJobEntity> allPackageSpu = new ArrayList<>();//需要更新的spu
        for (PackageGoodsJobEntity allPackageGood : allPackageGoods) {
            PackageGoodsJobEntity spuJobEntity = null;
            //subsku状态 是否存在或者变更-不存在或者有变更=true
            AtomicReference<Boolean> isChangeStatus = new AtomicReference<>(false);
            List<Byte> skuStatusList = new ArrayList<>();
            if (CollectionUtil.isEmpty(allPackageGood.getPackageGoodsSkuList())) {
                if (allPackageGood.getPackageStatus() != (byte) 0) {
                    spuJobEntity = new PackageGoodsJobEntity();
                    spuJobEntity.setId(allPackageGood.getId());
                    spuJobEntity.setPackageStatus((byte) 0);
                    allPackageSpu.add(spuJobEntity);
                    setLog(logEntityList, (byte) 2, allPackageGood.getId(), OperationLogTypeEnum.SPU_OFF_SHELF, null);
                }
            } else {
                allPackageGood.getPackageGoodsSkuList().forEach(packageGoodsSkuJobEntity -> {
                    List<Byte> substatusList = new ArrayList<>();
                    PackageGoodsSkuJobEntity skuJobEntity = null;
                    if (CollectionUtil.isEmpty(packageGoodsSkuJobEntity.getPackageSkuSubList())) {
                        if (packageGoodsSkuJobEntity.getPackageSkuStatus() != (byte) 0) {
                            skuJobEntity = new PackageGoodsSkuJobEntity();
                            skuJobEntity.setId(packageGoodsSkuJobEntity.getId());
                            skuJobEntity.setPackageSkuStatus((byte) 0);
                            skuJobEntity.setPackageSpuId(allPackageGood.getId());
                            allPackageSku.add(skuJobEntity);
                            isChangeStatus.set(true);
                            setLog(logEntityList, (byte) 2, allPackageGood.getId(), OperationLogTypeEnum.SKU_OFF_SHELF, packageGoodsSkuJobEntity.getId());
                        }
                        //设置套餐变更状态
                        skuStatusList.add((byte) 0);
                    } else {
                        for (PackageSkuSubJobEntity packageSkuSubJobEntity : packageGoodsSkuJobEntity.getPackageSkuSubList()) {
                            Byte couponType = packageSkuSubJobEntity.getCouponType();
                            String mapkey = CouponTypeEnum.getByType(couponType) + "_" + packageSkuSubJobEntity.getOuterId();
                            CouponGoodsSkuEntity couponGoodsSkuEntity = allCouponMap.get(mapkey);
                            PackageSkuSubJobEntity subJobEntity = null;
                            if (couponGoodsSkuEntity == null) {
                                subJobEntity = new PackageSkuSubJobEntity();
                                subJobEntity.setId(packageSkuSubJobEntity.getId());
                                subJobEntity.setCenterStatus((byte) 0);
                                subJobEntity.setSubSkuStatus((byte) 0);
                                substatusList.add((byte) 0);
                                isChangeStatus.set(true);
                                continue;
                            }
                            Byte subStatus = packageSkuSubJobEntity.getCenterStatus();
                            //(卡券类型=1 && (卡券状态变更 || 卡管面值变更 || 卡管库存变更 || 成本 || 卡管售价  ))
                            // ||
                            // (卡券类型=3  && ( 卡券状态变更 || 卡管面值变更  || 购买限制变更  || 成本 || 卡管售价 || 品诺直冲类型 || 品诺类型 ))
                            if (
                                    (
                                            couponType.intValue() == 1 &&
                                                    (
                                                            packageSkuSubJobEntity.getCenterStatus().intValue() != couponGoodsSkuEntity.getCenterStatus() ||
                                                                    couponGoodsSkuEntity.getAmount().compareTo(packageSkuSubJobEntity.getAmount()) != 0 ||
                                                                    couponGoodsSkuEntity.getInventory().intValue() != packageSkuSubJobEntity.getInventory().intValue() ||
                                                                    couponGoodsSkuEntity.getCostPrice().compareTo(packageSkuSubJobEntity.getCostPrice()) != 0 ||
                                                                    couponGoodsSkuEntity.getPrice().compareTo(packageSkuSubJobEntity.getPrice()) != 0
                                                    )
                                    ) || (
                                            couponType.intValue() == 3 &&
                                                    (couponGoodsSkuEntity.getAmount().compareTo(packageSkuSubJobEntity.getAmount()) != 0 ||
                                                            packageSkuSubJobEntity.getCenterStatus().intValue() != couponGoodsSkuEntity.getCenterStatus() ||
                                                            packageSkuSubJobEntity.getPnType().intValue() != couponGoodsSkuEntity.getPnType().intValue() ||
                                                            packageSkuSubJobEntity.getAccountType().intValue() != couponGoodsSkuEntity.getAccountType() ||
                                                            packageSkuSubJobEntity.getRationSaleNum().intValue() != couponGoodsSkuEntity.getRationSaleNum().intValue() ||
                                                            couponGoodsSkuEntity.getCostPrice().compareTo(packageSkuSubJobEntity.getCostPrice()) != 0 ||
                                                            couponGoodsSkuEntity.getPrice().compareTo(packageSkuSubJobEntity.getPrice()) != 0
                                                    )
                                    )
                            ) {
                                subJobEntity = new PackageSkuSubJobEntity();
                                subJobEntity.setId(packageSkuSubJobEntity.getId());
                                subJobEntity.setAmount(couponGoodsSkuEntity.getAmount());
                                subJobEntity.setPrice(couponGoodsSkuEntity.getPrice());
                                subJobEntity.setCostPrice(couponGoodsSkuEntity.getCostPrice());
                                if (couponType.intValue() == 1) {
                                    subJobEntity.setInventory(couponGoodsSkuEntity.getInventory());
                                } else if (couponType.intValue() == 3) {
                                    subJobEntity.setPnType(couponGoodsSkuEntity.getPnType());
                                    subJobEntity.setAccountType(couponGoodsSkuEntity.getAccountType().byteValue());
                                    subJobEntity.setRationSaleNum(couponGoodsSkuEntity.getRationSaleNum());
                                }
                                // 11:SKU跟随卡管信息变更
                                if (packageSkuSubJobEntity.getCenterStatus().intValue() == couponGoodsSkuEntity.getCenterStatus()) {
                                    setLog(logEntityList, (byte) 2, allPackageGood.getId(), OperationLogTypeEnum.SKU_FOLLOW_CARD_UPDATE, packageSkuSubJobEntity.getId());
                                } else {
                                    subJobEntity.setId(packageSkuSubJobEntity.getId());
                                    subJobEntity.setCenterStatus(couponGoodsSkuEntity.getCenterStatus().byteValue());
                                    subJobEntity.setSubSkuStatus(couponGoodsSkuEntity.getCenterStatus().byteValue());
                                    subStatus = couponGoodsSkuEntity.getCenterStatus().byteValue();
                                    isChangeStatus.set(true);
                                }
                            }
                            if (subJobEntity != null) {
                                allPackageSkuSub.add(subJobEntity);
                            }
                            substatusList.add(subStatus);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(substatusList)) {
                        Byte packageSkuStatus = packageGoodsSkuJobEntity.getPackageSkuStatus();
                        Byte manualDown = packageGoodsSkuJobEntity.getManualDown();
                        //isChangeStatus==true 表示 subStatus有变更
                        if (isChangeStatus.get() && manualDown.intValue() == 0) {
                            //1手动下架的
                            if (substatusList.contains((byte) 0) && packageSkuStatus.intValue() == 1) {
                                skuJobEntity = new PackageGoodsSkuJobEntity();
                                skuJobEntity.setId(packageGoodsSkuJobEntity.getId());
                                skuJobEntity.setPackageSkuStatus((byte) 0);
                                skuJobEntity.setPackageSpuId(allPackageGood.getId());
                                allPackageSku.add(skuJobEntity);
                                skuStatusList.add((byte) 0);
                                isChangeStatus.set(true);
                                setLog(logEntityList, (byte) 2, packageGoodsSkuJobEntity.getPackageSpuId(), OperationLogTypeEnum.SKU_OFF_SHELF, packageGoodsSkuJobEntity.getId());
                            } else if (!substatusList.contains((byte) 0) && packageSkuStatus.intValue() == 0) {
                                //当前状态为下架 & 手动状态为非手动下架
                                skuJobEntity = new PackageGoodsSkuJobEntity();
                                skuJobEntity.setId(packageGoodsSkuJobEntity.getId());
                                skuJobEntity.setPackageSkuStatus((byte) 1);
                                skuJobEntity.setPackageSpuId(allPackageGood.getId());
                                allPackageSku.add(skuJobEntity);
                                skuStatusList.add((byte) 1);
                                isChangeStatus.set(true);
                                setLog(logEntityList, (byte) 2, packageGoodsSkuJobEntity.getPackageSpuId(), OperationLogTypeEnum.SKU_ON_SHELF, packageGoodsSkuJobEntity.getId());
                            } else {
                                skuStatusList.add(packageSkuStatus);
                            }
                        } else {
                            skuStatusList.add(packageSkuStatus);
                        }
                    }
                });
            }
            Byte packageStatus = allPackageGood.getPackageStatus();
            if (isChangeStatus.get()) {
                if (skuStatusList.contains((byte) 1) && packageStatus.intValue() != 1) {
                    spuJobEntity = new PackageGoodsJobEntity();
                    spuJobEntity.setPackageStatus((byte) 1);
                    spuJobEntity.setId(allPackageGood.getId());
                    allPackageSpu.add(spuJobEntity);
                    setLog(logEntityList, (byte) 2, allPackageGood.getId(), OperationLogTypeEnum.SPU_ON_SHELF, null);
                } else if (!skuStatusList.contains((byte) 1) && packageStatus.intValue() != 0) {
                    spuJobEntity = new PackageGoodsJobEntity();
                    spuJobEntity.setPackageStatus((byte) 0);
                    spuJobEntity.setId(allPackageGood.getId());
                    allPackageSpu.add(spuJobEntity);
                    setLog(logEntityList, (byte) 2, allPackageGood.getId(), OperationLogTypeEnum.SPU_OFF_SHELF, null);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(allPackageSpu)) {
            packageGoodsRepository.updatePackageGoodsStatus(allPackageSpu);
            for (PackageGoodsJobEntity packageGoodsJobEntity : allPackageSpu) {
                redisUtil.delete(PACKAGE_SPU_INFO + packageGoodsJobEntity.getId());
            }
        }
        if (CollectionUtil.isNotEmpty(allPackageSku)) {
            packageGoodsSkuRepository.updatePackageGoodsSkuStatus(allPackageSku);
            for (PackageGoodsSkuJobEntity packageGoodsSkuJobEntity : allPackageSku) {
                redisUtil.delete(PACKAGE_SKU_INFO + packageGoodsSkuJobEntity.getPackageSpuId());
            }
        }
        if (CollectionUtil.isNotEmpty(allPackageSkuSub)) {
            packageSubSkuRepository.updatePackageSubSkuStatus(allPackageSkuSub);
            for (PackageSkuSubJobEntity packageSkuSubJobEntity : allPackageSkuSub) {
                redisUtil.delete(PACKAGE_SUB_SKU_INFO + packageSkuSubJobEntity.getPackageSkuId());
            }
        }
    }

    private void setCouponStatus(Map<String, CouponGoodsSkuEntity> allCouponMap, List<ProductOperatLogEntity> logEntityList) {
        List<CouponGoodsEntity> allCouponGoodsList = couponGoodsRepository.findAll();
        if (CollectionUtil.isEmpty(allCouponGoodsList)) {
            log.error("没有找到卡券商品sku");
            return;
        }
        for (CouponGoodsEntity couponGoodsEntity : allCouponGoodsList) {
            List<CouponGoodsSkuEntity> couponGoodsSkuList = couponGoodsEntity.getSkuList();
            int skuDownNum = 0; // sku 下架数量
            if (CollectionUtil.isEmpty(couponGoodsSkuList)) {
                log.error("卡券id{}商品sku为空", couponGoodsEntity.getCouponSpuId());
                continue;
            }
            for (CouponGoodsSkuEntity couponGoodsSku : couponGoodsSkuList) {
                CouponGoodsSkuEntity couponGoodsSkuTemp = BeanUtil.toBean(couponGoodsSku, CouponGoodsSkuEntity.class);
                String mapkey = CouponTypeEnum.getByType(couponGoodsEntity.getCouponType()) + "_" + couponGoodsSku.getCouponPlatformId();
                CouponGoodsSkuEntity newCouponGoodsSku = allCouponMap.get(mapkey);
                if (newCouponGoodsSku == null) {
                    log.warn("卡管未查询到当前卡数据,MapKey={},SKUID={}", mapkey, couponGoodsSku.getCouponSkuId());
                    //卡管未查询到的sku，直接下架
                    newCouponGoodsSku = new CouponGoodsSkuEntity();
                    newCouponGoodsSku.setCenterStatus(CouponStatus.DOWN.getType());
                }
                couponGoodsSkuTemp.copyUpdateData(newCouponGoodsSku);
                if (!couponGoodsSkuTemp.equals(couponGoodsSku)) {
                    //有数据变更，需要更新
                    couponGoodsSkuRepository.updateCouponGoodsSku(couponGoodsSkuTemp);
                    if (couponGoodsSkuTemp.getCenterStatus() == null || !couponGoodsSkuTemp.getCenterStatus().equals(couponGoodsSku.getCenterStatus())) {
                        if (couponGoodsSkuTemp.getCenterStatus() == 1) {
                            // 跟随上架日志
                            if (couponGoodsSku.getStatus().equals(0) && couponGoodsSku.getManualDown() == 0) {
                                setLog(logEntityList, (byte) 1, couponGoodsEntity.getCouponSpuId(), OperationLogTypeEnum.SKU_ON_SHELF, couponGoodsSkuTemp.getCouponSkuId());
                            }
                        } else {
                            if (couponGoodsSku.getStatus().equals(1)) {
                                setLog(logEntityList, (byte) 1, couponGoodsEntity.getCouponSpuId(), OperationLogTypeEnum.SKU_OFF_SHELF, couponGoodsSkuTemp.getCouponSkuId());
                            }
                        }
                    }
                    //卡管信息是否有变更
                    if (couponGoodsSkuTemp.getCenterStatus().equals(couponGoodsSku.getCenterStatus())) {
                        setLog(logEntityList, (byte) 1, couponGoodsEntity.getCouponSpuId(), OperationLogTypeEnum.SKU_FOLLOW_CARD_UPDATE, couponGoodsSkuTemp.getCouponSkuId());
                    }
                    //删除缓存
                    redisUtil.delete(COUPON_SKU_INFO + couponGoodsSkuTemp.getCouponSpuId());
                }
                if (couponGoodsSkuTemp.getCenterStatus().equals(0) || couponGoodsSkuTemp.getStatus().equals(0)) {
                    skuDownNum++;
                }
            }
            //sku全部下架，spu更改为下架
            if (couponGoodsEntity.getSkuList().size() == skuDownNum) {
                if (couponGoodsEntity.getStatus().equals(1)) {
                    couponGoodsEntity.setStatus(0);
                    couponGoodsRepository.updateCouponGoodsStatus(couponGoodsEntity);  //下架spu
                    setLog(logEntityList, (byte) 1, couponGoodsEntity.getCouponSpuId(), OperationLogTypeEnum.SPU_OFF_SHELF, null);
                    redisUtil.delete(COUPON_SPU_INFO + couponGoodsEntity.getCouponSpuId());
                    if (couponGoodsEntity.getCouponType() == 2) {
                        redisUtil.delete(COUPON_ALL_VIDEO_SPU_INFO);
                    }
                }
            } else {
                if (couponGoodsEntity.getStatus().equals(0)) {
                    couponGoodsEntity.setStatus(1);
                    couponGoodsRepository.updateCouponGoodsStatus(couponGoodsEntity);  //上架spu
                    setLog(logEntityList, (byte) 1, couponGoodsEntity.getCouponSpuId(), OperationLogTypeEnum.SPU_ON_SHELF, null);
                    redisUtil.delete(COUPON_SPU_INFO + couponGoodsEntity.getCouponSpuId());
                    if (couponGoodsEntity.getCouponType() == 2) {
                        redisUtil.delete(COUPON_ALL_VIDEO_SPU_INFO);
                    }
                }
            }
        }
    }

    private void setLog(List<ProductOperatLogEntity> logEntityList,
                        Byte productType,
                        Long spuId,
                        OperationLogTypeEnum operationType,
                        Long skuId
    ) {
        ProductOperatLogEntity logEntity = new ProductOperatLogEntity();
        logEntity.setSpuId(spuId);
        if (skuId != null) {
            logEntity.setSkuId(skuId);
        }
        logEntity.setProductType(productType);
        logEntity.setOperationType(operationType.getCode());
        logEntity.setSubmitterId(-1L);
        logEntity.setSubmitTime(new Date());
        logEntity.setAuditorId(-1L);
        logEntity.setAuditTime(new Date());
        logEntity.setCreateTime(new Date());
        logEntityList.add(logEntity);
    }


    /**
     * 获取卡管所有卡券
     *
     * @return
     */
    private Map<String, CouponGoodsSkuEntity> getKgCouponMap() {
        Map<String, CouponGoodsSkuEntity> allCouponMap = new HashMap<>();
        for (CouponTypeEnum type : CouponTypeEnum.values()) {
            List<CouponGoodsSkuEntity> platformCouponGoodsSkuEntity = couponPlatformFactory.getCouponPlatform(type).getAllCouponGoodsSku();
            if (CollectionUtil.isNotEmpty(platformCouponGoodsSkuEntity)) {
                allCouponMap.putAll(platformCouponGoodsSkuEntity.stream()
                        .collect(Collectors.toMap(
                                // key type 和 couponPlatformId 的组合
                                entity -> type + "_" + entity.getCouponPlatformId(),
                                entity -> entity,
                                (existing, replacement) -> replacement
                        )));
            }
        }
        return allCouponMap;
    }

    @Override
    public BaseResponse<CouponInfoResponseDTO> couponInfo(CouponInfoRequestDTO requestDTO) {
        CouponSkuInfoResponseDTO skuInfoResponseDTO = null;
        if (Objects.isNull(requestDTO.getCouponSkuId())) {
            //没有指定sku，就返回第一个规格下的第一个SKU
            //SPU的规格信息
            List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(requestDTO.getCouponSpuId(), ProductTypeEnum.COUPON.getType());
            if (CollectionUtil.isEmpty(specsEntityList)) {
                log.error("获取卡券详情--" + requestDTO.getCouponSpuId() + ":规格为空错误");
                BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }
            //这里只考虑单规格
            ProductSpecsEntity specsEntity = specsEntityList.get(0);
            if (CollectionUtil.isEmpty(specsEntity.getSpecsValueList())) {
                log.error("获取卡券详情--" + requestDTO.getCouponSpuId() + ":规格值为空错误");
                BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }

            if (Objects.nonNull(specsEntity.getSpecsName()) && !specsEntity.getSpecsName().equals("默认")
                    && specsEntity.getSpecsValueList().size() > 1) {
                //获取所有可售的sku
                List<CouponSkuInfoResponseDTO> responseList = getAllSellSkus(requestDTO.getCouponSpuId(), requestDTO.getRegionId());
                if (CollectionUtil.isEmpty(responseList)) {
                    log.error(requestDTO.getCouponSpuId() + ":在区域:" + requestDTO.getRegionId() + ",无可售sku");
                    return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
                }
                //根据规格值id进行分组
                Map<String, List<CouponSkuInfoResponseDTO>> skuMap = responseList.stream().collect(Collectors.groupingBy(CouponSkuInfoResponseDTO::getSpecsValue));
                for (ProductSpecsValueEntity specsValueEntity : specsEntity.getSpecsValueList()) {
                    List<CouponSkuInfoResponseDTO> skuList = skuMap.get(String.valueOf(specsValueEntity.getId()));
                    if (CollectionUtil.isNotEmpty(skuList)) {
                        skuInfoResponseDTO = skuList.get(0);
                        break;
                    }
                }
            }
        }

        CouponGoodsSkuEntity data = null;
        if (Objects.isNull(skuInfoResponseDTO)) {
            //获取指定的sku信息，若couponSkuId为空返回第一个区域可售sku
            List<CouponGoodsSkuEntity> skuEntityList = couponGoodsSkuRepository.getCouponGoodsSku(requestDTO.getCouponSpuId());
            if (CollectionUtil.isEmpty(skuEntityList)) {
                log.error("SPU:" + requestDTO.getCouponSpuId() + "的sku为空");
                return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }

            for (CouponGoodsSkuEntity item : skuEntityList) {
                if (Objects.isNull(requestDTO.getCouponSkuId())) {
                    if (productSkuSellRegionService.isSellableInRegion(requestDTO.getCouponSpuId(), item.getCouponSkuId(), ProductTypeEnum.COUPON, requestDTO.getRegionId())) {
                        //找到区域第一个可售sku
                        data = item;
                        break;
                    }
                } else if (requestDTO.getCouponSkuId().longValue() == item.getCouponSkuId().longValue()) {
                    //获取到指定的sku
                    if (!productSkuSellRegionService.isSellableInRegion(requestDTO.getCouponSpuId(), item.getCouponSkuId(), ProductTypeEnum.COUPON, requestDTO.getRegionId())) {
                        log.error("SPU:" + requestDTO.getCouponSpuId() + "的sku:" + requestDTO.getCouponSkuId() + "在区域:" + requestDTO.getRegionId() + "不可售");
                        return BaseResponse.fail(Status.REGION_NOT_SALE.getCode(), Status.REGION_NOT_SALE.getMessage());
                    }
                    data = item;
                    break;
                }
            }
            if (Objects.isNull(data)) {
                log.error("SPU:" + requestDTO.getCouponSpuId() + "在区域：" + requestDTO.getRegionId() + "没有可售的sku");
                return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }
            //获取选中/第一个可售的sku信息
            skuInfoResponseDTO = new CouponSkuInfoResponseDTO();
            BeanUtils.copyProperties(data, skuInfoResponseDTO);
        }

        skuInfoResponseDTO.setIsSelected(1);
        //获取促销活动信息
        PromotionSkuInfo promotionSkuInfo = promotionService.getPromotionInfoBySkuIdAndCouponType(skuInfoResponseDTO.getCouponSkuId(), ProductTypeEnum.COUPON.getType());
        if (Objects.nonNull(promotionSkuInfo)) {
            if (promotionSkuInfo.getActivityType().intValue() == PromotionActivityTypeEnum.DISCOUNT.getType().intValue()) {
                if (Objects.nonNull(promotionSkuInfo.getDiscount())) {
                    promotionSkuInfo.setActivityPrice(skuInfoResponseDTO.getAmount().multiply(new BigDecimal(promotionSkuInfo.getDiscount())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                } else {
                    promotionSkuInfo.setActivityPrice(skuInfoResponseDTO.getAmount());
                }
            }
            skuInfoResponseDTO.setPromotionInfo(promotionSkuInfo);
        }
        //获取优惠券信息
        List<BrandTicketResponseDTO> tickets = getTicketsBySku(skuInfoResponseDTO.getCouponSkuId(), requestDTO.getRegionId());
        skuInfoResponseDTO.setBrandTicketList(tickets);
        //获取spu信息
        CouponGoodsEntity goodsEntity = couponGoodsRepository.getCouponInfo(requestDTO.getCouponSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("卡券：" + requestDTO.getCouponSpuId() + "不存在");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }
        CouponInfoResponseDTO responseDTO = new CouponInfoResponseDTO();
        BeanUtils.copyProperties(goodsEntity, responseDTO);
        responseDTO.setExchangeList(BeanUtil.copyToList(goodsEntity.getExchangeList(), ProductExplainResponseDTO.class));
        responseDTO.setOffsetList(BeanUtil.copyToList(goodsEntity.getOffsetList(), ProductExplainResponseDTO.class));
        if (CollectionUtil.isNotEmpty(goodsEntity.getLableList())) {
            List<String> labels = goodsEntity.getLableList().stream().map(ProductLableEntity::getCouponLabelName).collect(Collectors.toList());
            responseDTO.setLabelList(labels);
        }
        responseDTO.setSkuInfoResponseDTO(skuInfoResponseDTO);

        //广告
        List<AdvertisementEntity> adList = advertisementService.getAdvertisementList(AdvertisementTypeEnum.ADV_COUPON.getCode(), requestDTO.getCouponSpuId());
        if (CollectionUtil.isNotEmpty(adList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            adList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(), ContentcenterTypeEnum.CONTENT_ADV.getCode(), requestDTO.getRegionId())) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            responseDTO.setAdvertiseList(advertisementInfoDTOList);
        }
        return BaseResponse.succeed(responseDTO);
    }

    @Override
    public CouponSkusResponseDTO skusInfo(CouponSkuInfoRequestDTO requestDTO, boolean needPromotion) {
        //获取所有可售的sku
        List<CouponSkuInfoResponseDTO> responseList = getAllSellSkus(requestDTO.getCouponSpuId(), requestDTO.getRegionId());
        if (CollectionUtil.isEmpty(responseList)) {
            log.error(requestDTO.getCouponSpuId() + ":在区域:" + requestDTO.getRegionId() + ",无可售sku");
            return null;
        }
        return getSkus(responseList, requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), needPromotion);
    }

    @Override
    public CouponSkuExtralInfoResponseDTO skuExtraInfo(CouponSkuExtraInfoRequestDTO requestDTO) {
        CouponSkuExtralInfoResponseDTO responseDTO = new CouponSkuExtralInfoResponseDTO();
        responseDTO.setCouponSpuId(requestDTO.getCouponSpuId());
        responseDTO.setCouponSkuId(requestDTO.getCouponSkuId());
        PromotionSkuInfo promotionSkuInfo = promotionService.getPromotionInfoBySkuIdAndCouponType(requestDTO.getCouponSkuId(), ProductTypeEnum.COUPON.getType());
        if (Objects.nonNull(promotionSkuInfo)) {
            if (promotionSkuInfo.getActivityType().intValue() == PromotionActivityTypeEnum.DISCOUNT.getType().intValue()) {
                //打折活动需要计算卡券打折后的金额
                List<CouponGoodsSkuEntity> skuEntityList = couponGoodsSkuRepository.getCouponGoodsSku(requestDTO.getCouponSpuId());
                CouponGoodsSkuEntity skuEntity = null;
                for (CouponGoodsSkuEntity couponGoodsSkuEntity : skuEntityList) {
                    if (couponGoodsSkuEntity.getCouponSkuId().longValue() == requestDTO.getCouponSkuId().longValue()) {
                        skuEntity = couponGoodsSkuEntity;
                        break;
                    }
                }
                if (Objects.nonNull(skuEntity)) {
                    if (Objects.nonNull(promotionSkuInfo.getDiscount())) {
                        promotionSkuInfo.setActivityPrice(skuEntity.getAmount().multiply(new BigDecimal(promotionSkuInfo.getDiscount())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                    } else {
                        promotionSkuInfo.setActivityPrice(skuEntity.getAmount());
                    }
                    log.info("卡券：" + skuEntity.getCouponSkuId() + "，面值：" + skuEntity.getAmount() + ",打折后价格：" + promotionSkuInfo.getActivityPrice() + ",折扣为：" + promotionSkuInfo.getDiscount());
                }
            }
            responseDTO.setPromotionInfo(promotionSkuInfo);
        }
        //优惠券
        List<BrandTicketResponseDTO> tickets = getTicketsBySku(requestDTO.getCouponSkuId(), requestDTO.getRegionId());
        /*List<TicketEntity> ticketList = ticketService.getTicketsBySkuIdAndPrdType(requestDTO.getCouponSkuId(), ProductTypeEnum.COUPON.getType());
        if (CollectionUtil.isNotEmpty(ticketList)) {
            log.info("获取单卡券：{},{}的优惠券信息，size:{}", requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), ticketList.size());
            tickets = new ArrayList<>();
            for (TicketEntity ticket : ticketList) {
                if ((Objects.nonNull(ticket.getIsNationwide()) && ticket.getIsNationwide() == 1)) {
                    tickets.add(BeanUtil.copyProperties(ticket, TicketResponseDTO.class));
                }
            }
        }*/
        responseDTO.setBrandTicketList(tickets);
        return responseDTO;
    }

    @Override
    public CouponShopDataResponseDTO getShopList(CouponShopRequestDTO requestDTO) {
        PickPlatformShopRequest paramEntity = new PickPlatformShopRequest();
        BeanUtils.copyProperties(requestDTO, paramEntity);
        paramEntity.setProductId(requestDTO.getPickProductId());
        RegionEntity region = regionRepository.getNearestCityByLocation(requestDTO.getLatitude(), requestDTO.getLongitude());
        if (Objects.isNull(region) || StringUtils.isEmpty(region.getAdcode())) {
            log.error("没有获取到城市编码：{}-{}-{}", requestDTO.getPickProductId(), requestDTO.getLatitude(), requestDTO.getLongitude());
            return null;
        }
        paramEntity.setCity_code(region.getAdcode());
        PickPlatformShopDataResponse dataResponse = pickPlatformGateway.getShopList(paramEntity);
        if (Objects.isNull(dataResponse) || CollectionUtil.isEmpty(dataResponse.getList())) {
            return null;
        }
        CouponShopDataResponseDTO responseDTO = new CouponShopDataResponseDTO();
        responseDTO.setCount(dataResponse.getCount());
        responseDTO.setShopList(new ArrayList<>());
        dataResponse.getList().forEach(item -> {
            CouponShopResponseDTO shopResponseDTO = new CouponShopResponseDTO();
            BeanUtils.copyProperties(item, shopResponseDTO);
            shopResponseDTO.setLogo(item.getSpecial_logo());
            responseDTO.getShopList().add(shopResponseDTO);
        });
        return responseDTO;
    }

    @Override
    public CouponCardCodeResponseDTO getCardCode(CouponCardCodeRequestDTO requestDTO) {
        PickPlatformCardCodeRequest request = new PickPlatformCardCodeRequest();
        request.setCheckNo(requestDTO.getCheckNo());
        request.setTimestamp(requestDTO.getTimestamp());
        request.setNonce(requestDTO.getNonce());
        request.setUserNo(requestDTO.getUserId());
        PickPlatformCardCodeResponse response = pickPlatformGateway.getCardCode(request);
        if (Objects.isNull(response)) {
            return null;
        }
        CouponCardCodeResponseDTO responseDTO = new CouponCardCodeResponseDTO();
        BeanUtils.copyProperties(response, responseDTO);
        return responseDTO;
    }

    @Override
    public BaseResponse<VideoCouponInfoResponseDTO> videoRechargeCouponsInfo(CouponSkuInfoRequestDTO requestDTO) {
        log.info("视听会员卡券videoRechargeCouponsInfo--{}:{}:{}", requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), requestDTO.getRegionId());
        //获取spu信息
        CouponGoodsEntity goodsEntity = couponGoodsRepository.getCouponInfo(requestDTO.getCouponSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "不存在");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }
        VideoCouponInfoResponseDTO responseDTO = new VideoCouponInfoResponseDTO();
        responseDTO.setIsShowPolymerize(goodsEntity.getIsShowPolymerize());
        log.info("是否在聚合页显示：" + goodsEntity.getIsShowPolymerize());
        if (goodsEntity.getIsShowPolymerize() == 1) {
            //需要显示聚合页
            List<CouponGoodsVideoEntity> videoEntityList = couponGoodsRepository.getVedioRechargeCoupons();
            if (CollectionUtil.isNotEmpty(videoEntityList)) {
                List<VideoCouponPolymerizeDetailResponseDTO> goodsVideoResponseList = new ArrayList<>();
                for (CouponGoodsVideoEntity couponGoodsVideoEntity : videoEntityList) {
                    VideoCouponPolymerizeDetailResponseDTO polymerizeDetailResponseDTO = new VideoCouponPolymerizeDetailResponseDTO();
                    BeanUtil.copyProperties(couponGoodsVideoEntity, polymerizeDetailResponseDTO);

                    if (requestDTO.getCouponSpuId().longValue() == polymerizeDetailResponseDTO.getCouponSpuId().longValue()) {
                        //与前端传过来的spu一致，标记为选中状态
                        polymerizeDetailResponseDTO.setIsSelected(1);
                        polymerizeDetailResponseDTO.setUserDetailList(BeanUtil.copyToList(goodsEntity.getUserDetailList(), ProductExplainResponseDTO.class));
                        polymerizeDetailResponseDTO.setWarmToastList(BeanUtil.copyToList(goodsEntity.getWarmToastList(), ProductExplainResponseDTO.class));

                        //获取视频直充SPU下的充值方式及选中的sku
                        BaseResponse<List<VideoCouponAccountTypeDTO>> data = getAccountType(requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), requestDTO.getRegionId());
                        if (data.getCode() != Status.LSucceed.getCode()) {
                            return BaseResponse.fail(data.getCode(), data.getMsg());
                        }
                        polymerizeDetailResponseDTO.setAccountTypeList(data.getResponse());
                    } else {
                        polymerizeDetailResponseDTO.setIsSelected(0);
                    }
                    goodsVideoResponseList.add(polymerizeDetailResponseDTO);
                }
                responseDTO.setGoodsVideoResponseList(goodsVideoResponseList);
            }
        } else {
            VideoCouponSingleDetailResponseDTO detailResponseDTO = new VideoCouponSingleDetailResponseDTO();
            BeanUtil.copyProperties(goodsEntity, detailResponseDTO);
            detailResponseDTO.setUserDetailList(BeanUtil.copyToList(goodsEntity.getUserDetailList(), ProductExplainResponseDTO.class));
            detailResponseDTO.setWarmToastList(BeanUtil.copyToList(goodsEntity.getWarmToastList(), ProductExplainResponseDTO.class));

            //获取所有可售的sku
            List<CouponSkuInfoResponseDTO> responseList = getAllSellSkus(requestDTO.getCouponSpuId(), requestDTO.getRegionId());
            if (CollectionUtil.isEmpty(responseList)) {
                log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "下没有可售的sku");
                return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }

            if (Objects.nonNull(requestDTO.getCouponSkuId())) {
                //若没有找到前端传过来的sku直接返回不可售，前端需要关闭详情页
                boolean find = false;
                for (CouponSkuInfoResponseDTO skuInfoResponseDTO : responseList) {
                    skuInfoResponseDTO.setIsSelected(0);
                    if (skuInfoResponseDTO.getCouponSkuId().longValue() == requestDTO.getCouponSkuId().longValue()) {
                        skuInfoResponseDTO.setIsSelected(1);
                        find = true;
                    }
                }
                if (!find) {
                    log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "指定的sku:" + requestDTO.getCouponSkuId() + "当前不可售");
                    return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
                }
            } else {
                //设置选中第一个
                responseList.get(0).setIsSelected(1);
            }
            detailResponseDTO.setSkuList(responseList);
            responseDTO.setSingleDetail(detailResponseDTO);
        }
        return BaseResponse.succeed(responseDTO);
    }

    @Override
    public BaseResponse<VideoCouponPolymerizeDetailResponseDTO> rechargeCouponDetail(CouponSkuInfoRequestDTO requestDTO) {
        log.info("视听会员卡券接口rechargeCouponDetail--{}:{}:{}}", requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), requestDTO.getRegionId());
        CouponGoodsEntity goodsEntity = couponGoodsRepository.getCouponInfo(requestDTO.getCouponSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "不存在");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }
        VideoCouponPolymerizeDetailResponseDTO responseDTO = new VideoCouponPolymerizeDetailResponseDTO();
        BeanUtil.copyProperties(goodsEntity, responseDTO);
        responseDTO.setIsSelected(1);
        responseDTO.setUserDetailList(BeanUtil.copyToList(goodsEntity.getUserDetailList(), ProductExplainResponseDTO.class));
        responseDTO.setWarmToastList(BeanUtil.copyToList(goodsEntity.getWarmToastList(), ProductExplainResponseDTO.class));
        //获取视频直充SPU下的充值方式及选中的sku
        BaseResponse<List<VideoCouponAccountTypeDTO>> data = getAccountType(requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), requestDTO.getRegionId());
        if (data.getCode() != Status.LSucceed.getCode()) {
            return BaseResponse.fail(data.getCode(), data.getMsg());
        }
        responseDTO.setAccountTypeList(data.getResponse());
        return BaseResponse.succeed(responseDTO);
    }

    @Override
    public BaseResponse<CouponSkusResponseDTO> getVideoSkuByAccount(AccountSkuInfoRequestDTO requestDTO) {
        log.info("视听会员卡券接口getVideoSkuByAccount--{}:{}:{}:{}", requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), requestDTO.getRegionId(), requestDTO.getAccountType());
        CouponGoodsEntity goodsEntity = couponGoodsRepository.getCouponInfo(requestDTO.getCouponSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "不存在");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }

        //获取所有可售sku
        List<CouponSkuInfoResponseDTO> allSkuList = getAllSellSkus(requestDTO.getCouponSpuId(), requestDTO.getRegionId());
        if (CollectionUtil.isEmpty(allSkuList)) {
            log.error("视听会员卡券SPU:" + requestDTO.getCouponSpuId() + "在区域：" + requestDTO.getRegionId() + "没有可售的sku");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }

        //查找出指定充值类型的所有sku
        List<CouponSkuInfoResponseDTO> skuList = allSkuList.stream()
                .filter(sku -> sku.getAccountType().intValue() == requestDTO.getAccountType())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuList)) {
            log.error("视听会员卡券：" + requestDTO.getCouponSpuId() + "的充值方式:" + requestDTO.getAccountType() + "下的sku为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }

        //组装SKU
        CouponSkusResponseDTO skusResponseDTO = getSkus(skuList, requestDTO.getCouponSpuId(), requestDTO.getCouponSkuId(), true);
        if (Objects.isNull(skusResponseDTO)) {
            log.error("视听会员卡券SPU:" + requestDTO.getCouponSpuId() + "的sku出现异常,所以聚合页不显示");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        return BaseResponse.succeed(skusResponseDTO);
    }

    @Override
    public BaseResponse<VedioShowTemplateResponseDTO> getVideoShowTemplate(VedioShowTemplateRequestDTO requestDTO) {
        log.info("视听会员卡券接口getVideoShowTemplate--{}", requestDTO.getCouponSpuId());
        CouponGoodsEntity goodsEntity = couponGoodsRepository.getCouponInfo(requestDTO.getCouponSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("视听会员卡券SPU：" + requestDTO.getCouponSpuId() + "不存在");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }
        log.info("卡券：{}的显示模版为：{}", requestDTO.getCouponSpuId(), goodsEntity.getShowTemplate());
        VedioShowTemplateResponseDTO responseDTO = new VedioShowTemplateResponseDTO();
        responseDTO.setShowTemplate(goodsEntity.getShowTemplate());
        responseDTO.setCouponSpuId(requestDTO.getCouponSpuId());
        return BaseResponse.succeed(responseDTO);
    }
/*@Override
    public List<CouponGoodsVideoResponseDTO> getVedioRechargeCoupons() {
        List<CouponGoodsVideoEntity> videoEntityList = couponGoodsRepository.getVedioRechargeCoupons();
        return BeanUtil.copyToList(videoEntityList, CouponGoodsVideoResponseDTO.class);
    }*/

    //获取所有可售的sku
    private List<CouponSkuInfoResponseDTO> getAllSellSkus(Long couponSpuId, Integer regionId) {
        List<CouponGoodsSkuEntity> skuEntityList = couponGoodsSkuRepository.getCouponGoodsSku(couponSpuId);
        if (CollectionUtil.isEmpty(skuEntityList)) {
            log.error(couponSpuId + ":sku为空错误");
            return null;
        }
        List<CouponSkuInfoResponseDTO> responseList = new ArrayList<>();
        for (CouponGoodsSkuEntity couponGoodsSkuEntity : skuEntityList) {
            //过滤掉区域不可售的sku
            if (productSkuSellRegionService.isSellableInRegion(couponSpuId,
                    couponGoodsSkuEntity.getCouponSkuId(),
                    ProductTypeEnum.COUPON,
                    regionId)) {
                CouponSkuInfoResponseDTO responseDTO = new CouponSkuInfoResponseDTO();
                BeanUtils.copyProperties(couponGoodsSkuEntity, responseDTO);
                responseDTO.setIsSelected(0);
                responseList.add(responseDTO);
            }
        }
        return responseList;
    }

    //按照前端UI样式组合SKU
    private CouponSkusResponseDTO getSkus(List<CouponSkuInfoResponseDTO> responseList, Long couponSpuId, Long couponSkuId, boolean needPromotion) {
        if (needPromotion) {
            //设置SKU的活动信息
            for (CouponSkuInfoResponseDTO responseDTO : responseList) {
                PromotionSkuInfo promotionSkuInfo = promotionService.getPromotionInfoBySkuIdAndCouponType(responseDTO.getCouponSkuId(), ProductTypeEnum.COUPON.getType());
                if (Objects.nonNull(promotionSkuInfo)
                        && promotionSkuInfo.getActivityType().intValue() == PromotionActivityTypeEnum.DISCOUNT.getType().intValue()) {
                    if (Objects.nonNull(promotionSkuInfo.getDiscount())) {
                        promotionSkuInfo.setActivityPrice(responseDTO.getAmount().multiply(new BigDecimal(promotionSkuInfo.getDiscount())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                    } else {
                        promotionSkuInfo.setActivityPrice(responseDTO.getAmount());
                    }
                }
                responseDTO.setPromotionInfo(promotionSkuInfo);
            }
        }
        CouponSkusResponseDTO skusResponseDTO = new CouponSkusResponseDTO();
        //SPU的规格信息
        List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(couponSpuId, ProductTypeEnum.COUPON.getType());
        if (CollectionUtil.isEmpty(specsEntityList)) {
            log.error(couponSpuId + ":规格为空错误");
            return null;
        }
        //这里只考虑单规格
        ProductSpecsEntity specsEntity = specsEntityList.get(0);
        List<CouponSkusResponseDTO.SkusSpecResponseDTO> list = new ArrayList<>();
        skusResponseDTO.setSkusSpecList(list);
        if (Objects.nonNull(specsEntity.getSpecsName()) && specsEntity.getSpecsName().equals("默认")) {
            skusResponseDTO.setSpecType(1);
            CouponSkusResponseDTO.SkusSpecResponseDTO skusSpecResponseDTO = new CouponSkusResponseDTO.SkusSpecResponseDTO();
            skusSpecResponseDTO.setSpecValueName("默认");
            skusSpecResponseDTO.setIsSelected(1);

            setSelectSku(couponSkuId, responseList);

            skusSpecResponseDTO.setSkuList(responseList);
            list.add(skusSpecResponseDTO);
        } else {
            skusResponseDTO.setSpecType(2);
            //规格值
            List<ProductSpecsValueEntity> valueList = specsEntity.getSpecsValueList();
            if (CollectionUtil.isEmpty(valueList)) {
                log.error(couponSpuId + ":规格值为空错误");
                return null;
            }
            //根据规格值id进行分组
            Map<String, List<CouponSkuInfoResponseDTO>> skuMap = responseList.stream().collect(Collectors.groupingBy(CouponSkuInfoResponseDTO::getSpecsValue));
            //选中的sku
            CouponSkuInfoResponseDTO selectSku = null;
            for (ProductSpecsValueEntity item : valueList) {
                List<CouponSkuInfoResponseDTO> skuList = skuMap.get(String.valueOf(item.getId()));
                if (CollectionUtil.isNotEmpty(skuList)) {
                    CouponSkusResponseDTO.SkusSpecResponseDTO skusSpecResponseDTO = new CouponSkusResponseDTO.SkusSpecResponseDTO();
                    skusSpecResponseDTO.setSpecValueName(item.getSpecsValue());
                    skusSpecResponseDTO.setIsSelected(0);

                    if (Objects.nonNull(couponSkuId) && Objects.isNull(selectSku)) {
                        //查找选中的sku
                        for (CouponSkuInfoResponseDTO responseDTO : skuList) {
                            if (responseDTO.getCouponSkuId().equals(couponSkuId)) {
                                selectSku = responseDTO;
                                break;
                            }
                        }
                        if (Objects.nonNull(selectSku)) {
                            //找到sku，规格和sku都选中
                            skusSpecResponseDTO.setIsSelected(1);
                            selectSku.setIsSelected(1);
//                            selectSku.setPromotionInfo(promotionService.getPromotionInfoBySkuIdAndCouponType(selectSku.getCouponSkuId(), ProductTypeEnum.COUPON.getType()));
                        }
                    }

                    skusSpecResponseDTO.setSkuList(skuList);
                    list.add(skusSpecResponseDTO);
                }
            }
            if (Objects.isNull(couponSkuId) || Objects.isNull(selectSku)) {
                //设置第一个规格及第一个sku为选中状态
                list.get(0).setIsSelected(1);
                selectSku = list.get(0).getSkuList().get(0);
                selectSku.setIsSelected(1);
//                selectSku.setPromotionInfo(promotionService.getPromotionInfoBySkuIdAndCouponType(selectSku.getCouponSkuId(), ProductTypeEnum.COUPON.getType()));
            }
        }
        return skusResponseDTO;
    }

    //在列表中标记选中的sku
    private void setSelectSku(Long selectedSkuId, List<CouponSkuInfoResponseDTO> list) {
        CouponSkuInfoResponseDTO selectSku = list.get(0);
        if (Objects.nonNull(selectedSkuId)) {
            for (CouponSkuInfoResponseDTO responseDTO : list) {
                if (responseDTO.getCouponSkuId().equals(selectedSkuId)) {
                    selectSku = responseDTO;
                    break;
                }
            }
        }
        selectSku.setIsSelected(1);
//        selectSku.setPromotionInfo(promotionService.getPromotionInfoBySkuIdAndCouponType(selectSku.getCouponSkuId(), ProductTypeEnum.COUPON.getType()));
    }

    //获取视频直充SPU下的充值方式及sku
    private BaseResponse<List<VideoCouponAccountTypeDTO>> getAccountType(Long couponSpuId, Long couponSkuId, Integer regionId) {
        //获取所有可售sku
        List<CouponSkuInfoResponseDTO> allSkuList = getAllSellSkus(couponSpuId, regionId);
        if (CollectionUtil.isEmpty(allSkuList)) {
            log.error("视听会员卡券SPU:" + couponSpuId + "在区域：" + regionId + "没有可售的sku");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        //查找选中的sku
        CouponSkuInfoResponseDTO selectSku = null;
        if (Objects.nonNull(couponSkuId)) {
            for (CouponSkuInfoResponseDTO skuInfoResponseDTO : allSkuList) {
                if (skuInfoResponseDTO.getCouponSkuId().longValue() == couponSkuId.longValue()) {
                    selectSku = skuInfoResponseDTO;
                    break;
                }
            }
            if (Objects.isNull(selectSku)) {
                log.error("视听会员卡券SPU:" + couponSpuId + "在区域：" + regionId + "没有查到sku:" + couponSkuId);
                return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
            }
        }
        // 统计卡券支持的所有充值方式
        List<Integer> accountTypes = allSkuList.stream()
                .map(CouponSkuInfoResponseDTO::getAccountType)
                .filter(Objects::nonNull)
                .distinct() // 去重
                .sorted(Comparator.reverseOrder()) // 倒序排序
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(accountTypes)) {
            log.error("视听会员卡券SPU：" + couponSpuId + "的充值方式为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SPU_SALE.getCode(), Status.NOT_HAVE_SPU_SALE.getMessage());
        }
        //组装充值方式
        List<VideoCouponAccountTypeDTO> accountTypeList = new ArrayList<>();
        VideoCouponAccountTypeDTO selectAccountType = null;//选中的充值方式
        for (Integer accountType : accountTypes) {
            VideoCouponAccountTypeDTO data = new VideoCouponAccountTypeDTO();
            data.setAccountType(accountType);
            data.setIsSelected(0);
            if (Objects.nonNull(selectSku) && selectSku.getAccountType().intValue() == accountType.intValue()) {
                //与前端传过来的sku充值方式一致，标记为选中状态
                data.setIsSelected(1);
                selectAccountType = data;
            }
            accountTypeList.add(data);
        }
        if (Objects.isNull(selectAccountType)) {
            //没有选中的充值方式，则选中第一个
            selectAccountType = accountTypeList.get(0);
            selectAccountType.setIsSelected(1);
        }
        // 过滤出选中充值方式的所有sku，因为未选中充值方式的sku不在这里返回
        final int accountType = selectAccountType.getAccountType().intValue();
        List<CouponSkuInfoResponseDTO> skuList = allSkuList.stream()
                .filter(sku -> sku.getAccountType().intValue() == accountType)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuList)) {
            log.error("视听会员卡券：" + couponSpuId + "的充值方式:" + accountType + "下的sku为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        //组装SKU
        CouponSkusResponseDTO skusResponseDTO = getSkus(skuList, couponSpuId, couponSkuId, true);
        if (Objects.isNull(skusResponseDTO)) {
            log.error("视听会员卡券SPU:" + couponSpuId + "的sku出现异常,所以聚合页不显示");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        selectAccountType.setSkuInfo(skusResponseDTO);
        return BaseResponse.succeed(accountTypeList);
    }

    //获取优惠券信息
    private List<BrandTicketResponseDTO> getTicketsBySku(Long couponSkuId, Integer regionId) {
        List<BrandTicketResponseDTO> brandTicket = new ArrayList<>();
        List<TicketEntity> ticketList = ticketService.getTicketsBySkuIdAndPrdType(couponSkuId, ProductTypeEnum.COUPON.getType());
        if (CollectionUtil.isNotEmpty(ticketList)) {
            log.info("获取单卡券sku：{}的优惠券信息，size:{}", couponSkuId, ticketList.size());
            List<TicketEntity> tickets = new ArrayList<>();
            for (TicketEntity ticket : ticketList) {
                if ((Objects.nonNull(ticket.getIsNationwide()) && ticket.getIsNationwide() == 1)
                        || ticketService.isOnlineInRegion(ticket.getTicketId(), regionId)) {
                    tickets.add(ticket);
                }
            }
            if (CollectionUtil.isNotEmpty(tickets)) {
                Map<Long, List<TicketEntity>> ticketMap = tickets.stream().collect(Collectors.groupingBy(TicketEntity::getBrandId));
                for (Map.Entry<Long, List<TicketEntity>> entry : ticketMap.entrySet()) {
                    List<TicketEntity> valueList = entry.getValue();
                    BrandTicketResponseDTO responseDTO = new BrandTicketResponseDTO();
                    responseDTO.setBrandName(valueList.get(0).getBrandName());
                    responseDTO.setTicketList(BeanUtil.copyToList(valueList, TicketResponseDTO.class));
                    brandTicket.add(responseDTO);
                }

                /*Map<Long, List<TicketEntity>> ticketMap = tickets.stream().collect(Collectors.groupingBy(TicketEntity::getBrandId));
                List<Long> brandIds = new ArrayList<>(ticketMap.keySet());
                List<TicketBrandEntity> brandList = ticketService.getTicketBrandsByIds(brandIds);
                if (CollectionUtil.isEmpty(brandList)) {
                    return null;
                }
                Map<Long, TicketBrandEntity> brandMap = brandList.stream().collect(Collectors.toMap(TicketBrandEntity::getId, brand -> brand));
                for (Map.Entry<Long, List<TicketEntity>> entry : ticketMap.entrySet()) {
                    Long brandId = entry.getKey();
                    List<TicketEntity> valueList = entry.getValue();
                    BrandTicketResponseDTO responseDTO = new BrandTicketResponseDTO();
                    responseDTO.setBrandName(brandMap.get(brandId).getBrandName());
                    responseDTO.setTicketList(BeanUtil.copyToList(valueList, TicketResponseDTO.class));
                    brandTicket.add(responseDTO);
                }*/
            }
        }
        return brandTicket;
    }
}
