package com.jsrxjt.mobile.biz.product.service.impl;

import com.jsrxjt.mobile.api.product.dto.request.ProductDialogDetailRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductDialogDetailResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductDialogCaseService;
import com.jsrxjt.mobile.domain.product.repository.ProductDialogRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Description: 产品弹框的业务编排层
 * @Author: ywt
 * @Date: 2025-11-06 09:29
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ProductDialogCaseServiceImpl implements ProductDialogCaseService {
    private final ProductDialogRepository productDialogRepository;

    @Override
    public ProductDialogDetailResponseDTO getDialogDetail(ProductDialogDetailRequestDTO requestDTO) {
        ProductDialogDetailResponseDTO responseDTO = new ProductDialogDetailResponseDTO();
        responseDTO.setContent(productDialogRepository.getDialog(requestDTO.getProductSpuId(), requestDTO.getDialogType(), requestDTO.getDialogType()));
        return responseDTO;
    }
}
