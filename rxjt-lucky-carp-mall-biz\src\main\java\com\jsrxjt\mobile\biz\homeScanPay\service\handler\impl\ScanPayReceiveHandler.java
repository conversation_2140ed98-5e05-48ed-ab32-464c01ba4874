package com.jsrxjt.mobile.biz.homeScanPay.service.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.ScanPayWssReceiveDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.ScanPayReceiverResponseDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.scanPay.request.UserCheckPassRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.UserHomePayRequestDTO;
import com.jsrxjt.mobile.api.scanPay.response.HomePayResultResponseDTO;
import com.jsrxjt.mobile.api.scanPay.types.ScanWsReceiveTypeEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanPayService;
import com.jsrxjt.mobile.biz.homeScanPay.service.handler.ScanReceiveHandler;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.gateway.PosV2PaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflinePayRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 扫码付支付接收处理
 * <AUTHOR>
 * @date 2025/11/04
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ScanPayReceiveHandler implements ScanReceiveHandler<ScanPayWssReceiveDTO> {

    private final CustomerService customerService;

    private final CustomerRepository customerRepository;

    private final OrderRepository orderRepository;

    private final PosV2PaymentGateway posV2PaymentGateway;
    @Override
    public boolean supports(ScanWsReceiveTypeEnum type) {
        return ScanWsReceiveTypeEnum.PAY.equals(type);
    }

    @Override
    public ScanPayReceiverResponseDTO handle(ScanPayWssReceiveDTO data) {
        ScanPayReceiverResponseDTO response = new ScanPayReceiverResponseDTO();
        if (data.getCustomerId() == null || data.getOrderNo() == null || data.getSource() == null){
            response.setType("WSS_ERROR");
            response.setCode(Status.NULL_PARAM.getCode());
            response.setMsg(Status.NULL_PARAM.getMessage());
            return response;
        }
        UserHomePayRequestDTO requestDTO = BeanUtil.copyProperties(data, UserHomePayRequestDTO.class);
        try {
            String orderNo = requestDTO.getOrderNo();
            Long customerId = requestDTO.getCustomerId();
            UserCheckPassRequestDTO request = new UserCheckPassRequestDTO();
            request.setCustomerId(requestDTO.getCustomerId());
            request.setPayPassword(requestDTO.getPayPassword());
            //检测密码-错误次数
            customerService.checkCustomerPsw(customerId, requestDTO.getPayPassword());
            CustomerEntity customerEntity = customerRepository.selectCustomerById(requestDTO.getCustomerId());
            //检测用户状态
            vaildUser(customerEntity);
            //验证订单状态
            OrderInfoEntity orderInfoEntity = orderRepository.findByOrderNo(orderNo);
            vailOrder(orderInfoEntity, customerId);
            Integer flatProductType = orderInfoEntity.getFlatProductType();
            if (flatProductType.equals(FlatProductTypeEnum.SCAN_OFFLINE_APP.getType())) {
                OfflinePayRequest offlinePayRequest = new OfflinePayRequest();
                offlinePayRequest.setTradeNo(orderInfoEntity.getTradeNo());
                offlinePayRequest.setSource(requestDTO.getSource());
                posV2PaymentGateway.pay(offlinePayRequest);
                //todo 首页扫码支付
            } else if (flatProductType.equals(FlatProductTypeEnum.SCAN_PICKUP_APP.getType())) {
                //todo 1展码付
                //todo 2订单支付结果回告
                // pickScanCaseService.
            }
        } catch (BizException e) {
            response.setType("WSS_ERROR");
            response.setMsg(e.getMsg());
            response.setCode(e.getCode());
            return response;
        } catch (Exception e){
            response.setType("WSS_ERROR");
            response.setCode(Status.WX_TRADE_PAYERROR.getCode());
            response.setMsg(Status.WX_TRADE_PAYERROR.getMessage());
            return response;
        }
        response.setCode(Status.LSucceed.getCode());
        response.setType("WSS_SUCCESS");
        return response;
    }

    private void vaildUser(CustomerEntity customerEntity) {
        if (customerEntity == null) {
            log.info("会员不存在");
            throw new BizException("会员不存在");
        }
        if (customerEntity.getDelFlag() == 1) {
            log.info("会员已删除");
            throw new BizException("会员已删除");
        }
        if (customerEntity.getStatus() != 1) {
            log.info("会员状态异常");
            throw new BizException("会员状态异常");
        }
        if (customerEntity.getPhone() == null) {
            log.info("会员手机号不存在");
            throw new BizException("会员手机号不存在");
        }
        if (customerEntity.getLoginStatus() == null || customerEntity.getLoginStatus() != 1) {
            log.info("登陆状态异常");
            throw new BizException("登陆状态异常");
        }
    }

    private void vailOrder(OrderInfoEntity orderInfoEntity, long customerId) {
        if (orderInfoEntity == null) {
            throw new BizException("订单不存在");
        }
        if (orderInfoEntity.getCustomerId() != customerId) {
            throw new BizException("订单不属于当前用户");
        }
        if (orderInfoEntity.getOrderStatus() != 0) {
            log.info("订单状态异常：{}", orderInfoEntity.getOrderNo());
            throw new BizException("订单状态异常");
        }
        Long payExpireTimestamp = orderInfoEntity.getPayExpireTimestamp();
        Date jdkDate = DateUtil.date(payExpireTimestamp * 1000).toJdkDate();
        long between = DateUtil.between(new Date(),jdkDate, DateUnit.SECOND);
        if (payExpireTimestamp == null || between < 0) {
            log.info("订单已过期：{}", orderInfoEntity.getOrderNo());
            throw new BizException("订单已过期");
        }
    }
}
