package com.jsrxjt.mobile.domain.product.service.factory.builder.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductSpecsRepository;
import com.jsrxjt.mobile.domain.product.service.factory.builder.ProductItemBuilder;
import com.jsrxjt.mobile.domain.promotion.service.PromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 套餐类型ProductItem构建器
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/27
 */
@Component("packageProductItemBuilder")
@RequiredArgsConstructor
@Slf4j
public class PackageProductItemBuilder implements ProductItemBuilder {

    private final PackageGoodsRepository packageGoodsRepository;

    private final PackageGoodsSkuRepository packageGoodsSkuRepository;

    private final ProductBrandRepository productBrandRepository;

    private final ProductSpecsRepository productSpecsRepository;

    private final PromotionService promotionService;

    @Override
    public ProductItem build(ProductItemId productItemId) {
        Long spuId = productItemId.getSpuId();

        // 1. 查询套餐SPU信息
        PackageGoodsEntity packageEntity = packageGoodsRepository.getPackageGoodsInfo(spuId);
        if (packageEntity == null) {
            log.warn("未找到SPU ID为 {} 的套餐商品", spuId);
            throw new BizException("未找到套餐商品");
        }
        ProductBrandEntity brand = productBrandRepository.findById(packageEntity.getBrandId());
        if (brand != null) {
            packageEntity.setBrandName(brand.getBrandName());
        }
        List<PackageGoodsSkuEntity> packageGoodsSkus = packageGoodsSkuRepository.getPackageGoodsSku(spuId);
        if (packageGoodsSkus == null || packageGoodsSkus.isEmpty()) {
            log.warn("未找到SPU ID为 {} 的套餐商品SKU", spuId);
            throw new BizException("未找到套餐商品SKU");
        }
        PackageGoodsSkuEntity matchedSku = packageGoodsSkus.stream()
                .filter(sku -> sku.getId().equals(productItemId.getSkuId()))
                .findFirst().orElse(null);
        // 找到了匹配的SKU，可以继续构建ProductItem
        if (matchedSku == null) {
            log.warn("未找到SKU ID为 {} 的套餐商品SKU", productItemId.getSkuId());
            throw new BizException("未找到的套餐商品SKU");
        }

        List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(spuId, ProductTypeEnum.PACKAGE.getType());
        Optional.ofNullable(specsEntityList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(ProductSpecsEntity::getSpecsValueList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(specs -> Objects.equals(specs.getId(), matchedSku.getSpecValueId()))
                .findFirst()
                .ifPresent(specs -> matchedSku.setSpecValueName(specs.getSpecsValue()));


        List<PackageSubSkuEntity> packageSubSku = packageGoodsSkuRepository.getPackageSubSku(matchedSku.getId());
        if (packageSubSku == null || packageSubSku.isEmpty()) {
            log.warn("未找到SKU ID为 {} 的套餐商品SKU的子SKU", productItemId.getSkuId());
            throw new BizException("未找到套餐商品SKU的子SKU");
        }

        // 2. 构建ProductItem
        return buildFromPackageData(packageEntity, matchedSku, packageSubSku);
    }

    private ProductItem buildFromPackageData(PackageGoodsEntity packageEntity, PackageGoodsSkuEntity skuEntity,
            List<PackageSubSkuEntity> subSkus) {
        ProductItem productItem = new ProductItem();

        // 设置标识
        productItem.setSpuId(packageEntity.getId());
        productItem.setSkuId(skuEntity.getId()); // 设置SKU ID
        productItem.setProductType(ProductTypeEnum.PACKAGE.getType()); // 套餐类型
        productItem.setFlatProductType(FlatProductTypeEnum.PACKAGE.getType()); // 扁平化类型：套餐

        // 设置商品基本信息
        productItem.setProductName(packageEntity.getPackageSpuName());
        productItem.setProductLogo(packageEntity.getLogoUrl());
        productItem.setImgUrl(packageEntity.getImgUrl());
        productItem.setBrandId(packageEntity.getBrandId());
        productItem.setBrandName(packageEntity.getBrandName());
        productItem.setChannelId(packageEntity.getChannelId());
        productItem.setChannelName(packageEntity.getChannelName());
        productItem.setStatus(packageEntity.getPackageStatus());
        productItem.setPayType(packageEntity.getPayType());
        productItem.setFirstCategoryId(packageEntity.getFirstCatId());
        productItem.setCategoryId(packageEntity.getSecondCatId());

        // 设置SKU相关的价格和限购信息
        productItem.setPlatformPrice(skuEntity.getPlatformPrice()); // 平台售价
        productItem.setDefaultServiceFeePercent(skuEntity.getCommissionFee()); // 手续费比例
        productItem.setLimitNumPerMonth(skuEntity.getLimitNumPerMonth()); // 每月限购数量

        // 设置规格值信息
        productItem.setSpecsValue(skuEntity.getSpecValue()); // 规格值

        productItem.setSpecsValueName(skuEntity.getSpecValueName());

        // 设置数据来源表
        productItem.setSourceTable("package_sku");

        // 从子SKU中计算最小面额作为套餐面额
        if (!subSkus.isEmpty()) {
            // 计算面额 - 子SKU面额之和
            BigDecimal totalAmount = subSkus.stream()
                    .map(PackageSubSkuEntity::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            productItem.setFaceAmount(totalAmount);

            // 计算最小库存作为套餐可用库存
            Integer minInventory = subSkus.stream()
                    .map(PackageSubSkuEntity::getInventory)
                    .filter(Objects::nonNull)
                    .min(Integer::compareTo)
                    .orElse(0);
            productItem.setInventory(minInventory);

            // 计算平均成本价
            BigDecimal avgCostPrice = subSkus.stream()
                    .map(PackageSubSkuEntity::getCostPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(subSkus.size()), 2, RoundingMode.HALF_UP);
            productItem.setCostPrice(avgCostPrice);

        }
        productItem.setPackageSubSkus(subSkus);

        // 查询促销活动信息
        PromotionSkuInfo promotionSkuInfo = promotionService
                .getInProcessPromotionInfo(skuEntity.getId(), ProductTypeEnum.PACKAGE.getType());
        productItem.setPromotionSkuInfo(promotionSkuInfo);

        log.info("成功构建套餐ProductItem: spuId={}, skuId={}, 子SKU数量={}",
                packageEntity.getId(), skuEntity.getId(), subSkus.size());

        return productItem;
    }
}