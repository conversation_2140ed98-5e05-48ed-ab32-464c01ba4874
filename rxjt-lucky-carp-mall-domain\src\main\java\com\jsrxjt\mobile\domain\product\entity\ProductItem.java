package com.jsrxjt.mobile.domain.product.entity;

import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 产品项
 * 
 * <AUTHOR>
 * @since 2025/5/27
 **/
@Data
public class ProductItem {
    /**
     * spuId
     */
    private Long spuId;

    /**
     * skuId 没有sku的产品默认为0
     */
    private Long skuId;
    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品类型（扁平化）
     */
    private Integer flatProductType;

    /**
     * 平台售价（不含手续费）
     */
    private BigDecimal platformPrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 默认加点手续费比例
     */
    private BigDecimal defaultServiceFeePercent;
    /**
     * 应用标记，针对应用类产品有值
     */
    private String appFlag;
    /**
     * 库存数量
     */
    private Integer inventory;

    /**
     * 每月限购数量
     */
    private Integer limitNumPerMonth;

    /**
     * 每日限购数量
     */
    private Integer limitNumPerDay;

    /**
     * 状态 0下架 1上架
     */
    private Integer status;
    /**
     * 品牌id
     */
    private Long brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 渠道id
     */
    private Long channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 一级类目Id
     */
    private Long firstCategoryId;

    /**
     * 最低一级的类目Id
     */
    private Long categoryId;


    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品logo
     */
    private String productLogo;

    /**
     * 缩略图图片地址
     */
    private String imgUrl;

    /**
     * 外部产品ID 卡券产品对应 coupon_goods_sku的 coupon_platform_id
     */
    private String outProductId;

    /**
     * 面额名称
     */
    private String faceName;

    /**
     * 面额
     */
    private BigDecimal faceAmount;
    /**
     * 数据来源表名，例如：coupon_goods_sku等
     */
    private String sourceTable;
    /**
     * 规格值id，多个规格值用逗号分隔'
     */
    private String specsValue;

    /**
     * 规格值名称
     */
    private String specsValueName;
    /**
     * 促销活动信息 如果存在活动，不为空，否则为null
     */
    private PromotionSkuInfo promotionSkuInfo;
    /**
     * 支持的支付类型
     */
    private String payType;

    /**
     * 套餐子SKU列表 套餐产品有值，否则为空
     */
    private List<PackageSubSkuEntity> packageSubSkus;

    /**
     * 支付方式是否为同步，0--异步 1--同步。若type=4本字段为提货券分销中台提供
     */
    private Integer isSyncPayment;

    /**
     * 下单时是否需要验证码验证 0 不需要 1 需要
     */
    private Boolean needCaptcha;

    /**
     * 领域行为：检查是否可售
     */
    public boolean isAvailable() {
        return status != null && status == 1 && (inventory == null || inventory > 0);
    }

    public boolean hasLimitNumPerMonth() {
        return limitNumPerMonth != null && limitNumPerMonth > 0 && limitNumPerMonth < Integer.MAX_VALUE;
    }

    /**
     * 领域行为：检查是否有SKU
     */
    public boolean hasSku() {
        return skuId != null && skuId > 0;
    }

    public boolean hasReleaseInventoryProp() {
        return Objects.equals(productType, ProductTypeEnum.COUPON.getType())
                || Objects.equals(productType, ProductTypeEnum.PACKAGE.getType());
    }

    /**
     * 领域行为：获取产品类型枚举
     */
    public ProductTypeEnum getProductTypeEnum() {
        return ProductTypeEnum.getByType(productType);
    }

    /**
     * 领域行为：验证产品完整性
     */
    public void validate() {
        if (spuId == null || spuId <= 0) {
            throw new IllegalArgumentException("SPU ID不能为空或小于等于0");
        }
        if (productType == null || !ProductTypeEnum.isValidType(productType)) {
            throw new IllegalArgumentException("产品类型无效");
        }
    }

    /**
     * 获取产品来源渠道枚举
     * 根据产品类型和扁平化产品类型判断订单渠道
     */
    public OrderChannelEnum getProductSourceChannel() {
        // 优先根据产品类型判断
        if (productType != null && (productType == 1 || productType == 2)) {
            return OrderChannelEnum.CARD_MANAGEMENT;
        }

        // 根据扁平化产品类型判断
        if (flatProductType != null) {
            return switch (flatProductType) {
                case 301 -> OrderChannelEnum.DISTRIBUTION_CENTER;
                case 402 -> OrderChannelEnum.ALIPAY_RED_ENVELOPE;
                case 403 -> OrderChannelEnum.SUXI_TELECOM;
                case 407 -> OrderChannelEnum.BIAN_LI_FENG;
                case 304 -> OrderChannelEnum.SCAN_PAY;
                case 306 -> OrderChannelEnum.OFFLINE_SCAN;
                default -> OrderChannelEnum.UNKNOWN;
            };
        }

        // 默认返回未知渠道
        return OrderChannelEnum.UNKNOWN;
    }

    public boolean hasLimitNumPerDay() {
        return limitNumPerDay != null && limitNumPerDay > 0 && limitNumPerDay < Integer.MAX_VALUE;
    }

    public boolean isNeedCaptcha() {
        return needCaptcha != null && needCaptcha;
    }
}
