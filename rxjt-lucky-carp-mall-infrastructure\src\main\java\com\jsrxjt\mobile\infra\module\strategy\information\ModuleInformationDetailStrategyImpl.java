package com.jsrxjt.mobile.infra.module.strategy.information;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.module.annotation.ModuleDetailTypeHandler;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.module.entity.ModuleDetailEntity;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import com.jsrxjt.mobile.infra.coupon.persistent.po.CouponGoodsPO;
import com.jsrxjt.mobile.infra.information.persistent.mapper.InformationMapper;
import com.jsrxjt.mobile.infra.information.persistent.po.InformationPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;

/**
 * 资讯
 * <AUTHOR>
 * @date 2025/07/01
 */
@ModuleDetailTypeHandler({ModuleDetailType.SINGLE_INFORMATION})
@Component
@RequiredArgsConstructor
@Slf4j
public class ModuleInformationDetailStrategyImpl extends ModuleDetailStrategy {

    private final ContentRegionService contentRegionService;

    private final InformationMapper informationMapper;

    @Override
    public ModuleDetailEntity updateModuleDetailEntityInfo(ModuleDetailEntity moduleDetailEntity) {
        String productIdStr = moduleDetailEntity.getProductId();
        if (StringUtils.isEmpty(productIdStr)) {
            log.error("未获取到资讯id");
            return moduleDetailEntity;
        }
        Long productId = Long.valueOf(productIdStr);
        //获取商品当前区域是否可售
        if (moduleDetailEntity.getCityId() != null && moduleDetailEntity.getDistrictId() != null){
            Integer regionId = null;
            if (!moduleDetailEntity.getDistrictId().equals(0)){
                regionId = moduleDetailEntity.getDistrictId();
            }else {
                regionId = moduleDetailEntity.getCityId();
            }
            moduleDetailEntity.setOnSale(contentRegionService.isOnlineInRegion(Long.valueOf(moduleDetailEntity.getProductId()), ContentcenterTypeEnum.CONTENT_INFO.getCode(), regionId));
        }
        InformationPO informationPO = informationMapper.selectOne(new LambdaQueryWrapper<InformationPO>()
                .eq(InformationPO::getInformationId, productId)
                .eq(InformationPO::getDelFlag, 0)
                .last("LIMIT 1"));
        Date now = new Date();
        if (informationPO == null ){
            moduleDetailEntity.setOnSale(false);
            updateModuleDetailEntityStatusOrDel(moduleDetailEntity.getDetailId(), null, 1);
            log.info("资讯已删除 informationId={}", productId);
            return moduleDetailEntity;
        }
        if (now.after(informationPO.getEndTime())) {
                moduleDetailEntity.setOnSale(false);
                updateModuleDetailEntityStatusOrDel(moduleDetailEntity.getDetailId(), 0, informationPO.getDelFlag());
                log.info("资讯已过期 informationId={}", informationPO.getInformationId());
                return moduleDetailEntity;
        }
        if (StringUtils.isNotBlank(informationPO.getLabel())){
            moduleDetailEntity.setPromotionLabels(Arrays.asList(informationPO.getLabel()));
        }
        moduleDetailEntity.setProductOriginalName(informationPO.getInformationTitle());
        moduleDetailEntity.setProductOriginalSubName(informationPO.getSubTitle());
        moduleDetailEntity.setProductImgUrl(informationPO.getImgUrl());
        return moduleDetailEntity;
    }

}
