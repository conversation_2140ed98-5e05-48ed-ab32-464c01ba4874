package com.jsrxjt.mobile.biz.lock;

import java.util.concurrent.TimeUnit;

/**
 *  DistributedLock
 *  分布式锁接口
 * <AUTHOR>
 * 2023/4/3 15:47
 * 
 **/
public interface DistributedLock {
    boolean tryLock(String lockName);

    boolean tryLock(String lockName, long timeout, TimeUnit unit) throws InterruptedException;

    boolean isLock(String lockName);

    void lock(String lockName);

    void lock(String lockName, long timeout, TimeUnit unit);

    void unLock(String lockName);

}
