package com.jsrxjt.mobile.biz.xcyMall.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCancelOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallCreateOrderRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallQueryUserRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.request.XcyMallRefundRequestDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCancelResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallQueryUserResponseDTO;
import com.jsrxjt.mobile.api.xcy.dto.response.XcyMallRefundResponseDTO;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.biz.xcyMall.IXcyMallService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.XcyMallOrderInfoBuilder;
import com.jsrxjt.mobile.domain.xcyMall.gateway.IXcyMallGateWay;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Created by jeffery.yang on 2025/10/21 15:36
 *
 * @description: 祥采云service
 * @author: jeffery.yang
 * @date: 2025/10/21 15:36
 * @version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class XcyMallServiceImpl implements IXcyMallService {

	private final CustomerService customerService;
	private final IXcyMallGateWay xcyMallGateWay;
	private final AppGoodsRepository appGoodsRepository;
	private final DistributedLock distributedLock;
	private final OrderRepository orderRepository;
	private final OrderCaseService orderCaseService;
	private final XcyMallOrderInfoBuilder xcyMallOrderInfoBuilder;
	private final AutoRefundCaseService autoRefundCaseService;

	private final AfterSaleCaseService afterSaleCaseService;

	private static final String ORDER_LOCK_PREFIX = "xcyMall_order_notify_lock:";
	private static final String REFUND_LOCK_PREFIX = "xcyMall_refund_notify_lock:";
	private static final int ERROR_CODE = 1;

	@Override
	public XcyMallQueryUserResponseDTO getVipInfo(XcyMallQueryUserRequestDTO requestDTO, String appFlag) {
		return processRequest(requestDTO, appFlag, "查询用户",
							  () -> validateRequiredParams(requestDTO.getAuthCode(), requestDTO.getAppid(),
														   requestDTO.getTimestamp(), requestDTO.getNounce(), requestDTO.getSign()),
							  () -> {
								  AppGoodsEntity appGoods = getAppGoods(appFlag);
								  CustomerDetailResponse customerDetail = customerService.getCustomerInfoByOnceToken(requestDTO.getAuthCode());
								  return xcyMallGateWay.buildGetVipInfoResponse(customerDetail, appGoods);
							  }, XcyMallQueryUserResponseDTO.builder()::resCode, XcyMallQueryUserResponseDTO.builder()::resDesc);
	}

	@Override
	public XcyMallCreateOrderResponseDTO createOrder(XcyMallCreateOrderRequestDTO requestDTO, String appFlag) {
		return processRequest(requestDTO, appFlag, "下单",
							  () -> validateAmount(requestDTO.getTotalAmount()),
							  () -> withLock(ORDER_LOCK_PREFIX + requestDTO.getTradeNo(), () -> {
								  AppGoodsEntity appGoods = getAppGoods(appFlag);
								  validateOrderExists(requestDTO.getTradeNo(), appFlag);

								  CustomerEntity customer = getCustomer(requestDTO.getUid());
								  OrderInfoEntity orderInfo = submitOrder(requestDTO, appGoods, customer);
								  return xcyMallGateWay.buildCreateOrderResponse(orderInfo, appFlag);
							  }), XcyMallCreateOrderResponseDTO.builder()::resCode, XcyMallCreateOrderResponseDTO.builder()::resDesc);
	}

	@Override
	public XcyMallRefundResponseDTO refundOrder(XcyMallRefundRequestDTO requestDTO, String appFlag) {
		return processRequest(requestDTO, appFlag, "退款",
							  () -> validateAmount(requestDTO.getRefundAmount()),
							  () -> {
								  AppGoodsEntity appGoods = getAppGoods(appFlag);
								  OrderInfoEntity order = getOrder(requestDTO.getTradeNo(), appFlag);
								  validateRefundStatus(order.getPaymentStatus());

								  return processRefund(order, requestDTO, appFlag);
							  }, XcyMallRefundResponseDTO.builder()::resCode, XcyMallRefundResponseDTO.builder()::resDesc);
	}

	@Override
	public XcyMallCancelResponseDTO cancelOrder(XcyMallCancelOrderRequestDTO requestDTO, String appFlag) {
		return processRequest(requestDTO, appFlag, "取消订单",
							  () -> {},
							  () -> {
								  AppGoodsEntity appGoods = getAppGoods(appFlag);
								  OrderInfoEntity order = getOrder(requestDTO.getTradeNo(), appFlag);
								  orderCaseService.cancelOrder(order.getOrderNo(), order.getCustomerId());
								  return xcyMallGateWay.buildCancelOrderResponse(appGoods, appFlag);
							  }, XcyMallCancelResponseDTO.builder()::resCode, XcyMallCancelResponseDTO.builder()::resDesc);
	}

	/**
	 * 通用请求处理器
	 */
	private <T, R> R processRequest(T requestDTO, String appFlag, String operation,
									Runnable validator, Supplier<R> processor,
									Function<Integer, ?> errorBuilder, Function<String, ?> descBuilder) {
		log.info("祥采云{}请求参数：appFlag:{},request:{}", operation, appFlag, JSONUtil.toJsonStr(requestDTO));

		try {
			// 验签
			if (!verifySign(requestDTO, appFlag)) {
				return buildErrorResponse(errorBuilder, descBuilder, "验签失败");
			}

			// 参数验证
			validator.run();

			// 业务处理
			R result = processor.get();
			log.info("祥采云{}返回参数：{}", operation, JSONUtil.toJsonStr(result));
			return result;

		} catch (BizException e) {
			log.error("处理祥采云{}业务异常：{}", operation, e.getMsg(), e);
			return buildErrorResponse(errorBuilder, descBuilder, e.getMsg());
		} catch (Exception e) {
			log.error("处理祥采云{}系统异常：{}", operation, e.getMessage(), e);
			return buildErrorResponse(errorBuilder, descBuilder, operation + "异常");
		}
	}

	/**
	 * 带锁执行
	 */
	private <T> T withLock(String lockKey, Supplier<T> processor) {
		try {
			boolean lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
			if (!lockAcquired) {
				throw new BizException("系统繁忙，请稍后重试");
			}
			return processor.get();
		} catch (Exception e) {
			log.error("处理祥彩云业务异常：{}", e.getMessage(), e);
		} finally {
			distributedLock.unLock(lockKey);
		}
		return null;
	}

	/**
	 * 退款处理
	 */
	private XcyMallRefundResponseDTO processRefund(OrderInfoEntity order, XcyMallRefundRequestDTO requestDTO, String appFlag) {
		return withLock(REFUND_LOCK_PREFIX + requestDTO.getTradeNo(), () -> {
			BigDecimal refundAmount = new BigDecimal(requestDTO.getRefundAmount());
			BigDecimal orderAmount = order.getTotalSellAmount();

			AutoRefundRequestDTO autoRefundRequest = new AutoRefundRequestDTO();
			autoRefundRequest.setOrderNo(order.getOrderNo());
			autoRefundRequest.setExternalRefundNo(requestDTO.getRefundNo());
			autoRefundRequest.setApplyRefundAmount(refundAmount);
			autoRefundRequest.setExternalRefundAmount(refundAmount);

			AfterSaleEntity afterSaleEntity = new AfterSaleEntity();
			Integer paymentStatus = order.getPaymentStatus();
			if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_PAID.getCode())) {
				throw new BizException("订单状态不可退");
			}else if (Objects.equals(paymentStatus, PaymentStatusEnum.PAID.getCode())
				|| Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDING.getCode())
				|| Objects.equals(paymentStatus, PaymentStatusEnum.PARTIAL_REFUNDED.getCode())) {
				if (orderAmount.compareTo(refundAmount) == 0) {
					// 整单退场景需要退手续费
					autoRefundRequest.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
					autoRefundRequest.setApplyRefundAmount(order.getOrderAmount());
					afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequest);
				} else if (orderAmount.compareTo(refundAmount) > 0) {
					// 部分退
					autoRefundRequest.setAfterSaleType(AfterSaleTypeEnum.PARTIAL_REFUND.getCode());
					autoRefundRequest = afterSaleCaseService.calDistributionOrderRefundAmount(autoRefundRequest);
					afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequest);
				} else {
					throw new BizException("退款金额超出订单金额");
				}
			}else if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode()) || Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDING.getCode())) {
				throw new BizException("已整单退款,不可再退");
			}
			return xcyMallGateWay.buildRefundOrderResponse(afterSaleEntity, appFlag);
		});
	}

	/**
	 * 提交订单
	 */
	private OrderInfoEntity submitOrder(XcyMallCreateOrderRequestDTO requestDTO, AppGoodsEntity appGoods, CustomerEntity customer) {

		CreateOrderDTO createOrderDTO = new CreateOrderDTO();
		createOrderDTO.setCustomerId(customer.getId());
		createOrderDTO.setCustomerMobile(customer.getPhone());
		createOrderDTO.setExternalOrderNo(requestDTO.getTradeNo());
		createOrderDTO.setProductSpuId(appGoods.getAppId());
		createOrderDTO.setProductType(ProductTypeEnum.APP.getType());
		createOrderDTO.setExternalAppProductPrice(new BigDecimal(requestDTO.getTotalAmount()));
		createOrderDTO.setExternalPayResultUrl(requestDTO.getResultPageUrl());
		createOrderDTO.setOrderDetailUrl(requestDTO.getOrderDetailUrl());
		createOrderDTO.setExternalOrderExpireTime(String.valueOf(getExpireTimestamp(requestDTO.getOrderTime(), requestDTO.getOrderExpire())));

		return orderCaseService.submitOrder(createOrderDTO, xcyMallOrderInfoBuilder);
	}

	/**
	 * 验证工具方法
	 */
	private void validateRequiredParams(String... params) {
		for (String param : params) {
			if (StringUtils.isBlank(param)) {
				throw new BizException("缺少参数");
			}
		}
	}

	private void validateAmount(String amount) {
		if (new BigDecimal(amount).compareTo(BigDecimal.ZERO) < 0) {
			throw new BizException("金额非法");
		}
	}

	private void validateOrderExists(String tradeNo, String appFlag) {
		if (orderRepository.findByExternalOrderNoAndAppFlag(tradeNo, appFlag) != null) {
			throw new BizException("此订单已存在");
		}
	}

	private void validateRefundStatus(Integer paymentStatus) {
		if (Objects.equals(paymentStatus, PaymentStatusEnum.UNPAID.getCode())) {
			throw new BizException("订单状态不可退");
		}
		if (Objects.equals(paymentStatus, PaymentStatusEnum.FULL_REFUNDED.getCode())) {
			throw new BizException("已整单退款,不可再退");
		}
	}

	/**
	 * 数据获取工具方法
	 */
	private AppGoodsEntity getAppGoods(String appFlag) {
		AppGoodsEntity appGoods = appGoodsRepository.findAppGoodsByAppFlag(appFlag);
		if (appGoods == null) {
			throw new BizException("应用不存在");
		}
		return appGoods;
	}

	private CustomerEntity getCustomer(String uid) {
		CustomerEntity customer = customerService.getCustomerById(Long.valueOf(uid));
		if (customer == null) {
			throw new BizException("用户不存在");
		}
		return customer;
	}

	private OrderInfoEntity getOrder(String tradeNo, String appFlag) {
		OrderInfoEntity order = orderRepository.findByExternalOrderNoAndAppFlag(tradeNo, appFlag);
		if (order == null) {
			throw new BizException("订单不存在");
		}
		return order;
	}

	/**
	 * 验签
	 */
	private Boolean verifySign(Object requestDTO, String appFlag) {
		Map requestMap = JSONObject.parseObject(JSON.toJSONString(requestDTO), Map.class);
		return xcyMallGateWay.verifySign(requestMap, appFlag);
	}

	@SuppressWarnings("unchecked")
	private <R> R buildErrorResponse(Function<Integer, ?> errorBuilder, Function<String, ?> descBuilder, String message) {
		Object builder = errorBuilder.apply(ERROR_CODE);
		descBuilder.apply(message);
		try {
			Method buildMethod = builder.getClass().getMethod("build");
			buildMethod.setAccessible(true);
			return (R) buildMethod.invoke(builder);
		} catch (Exception e) {
			throw new RuntimeException("构建错误响应失败", e);
		}
	}


	private  long getExpireTimestamp(String orderTime, String orderExpire) {
		return LocalDateTime.parse(orderTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
			.plusMinutes(Integer.parseInt(orderExpire))
			.toEpochSecond(ZoneOffset.of("+8"));
	}

}
