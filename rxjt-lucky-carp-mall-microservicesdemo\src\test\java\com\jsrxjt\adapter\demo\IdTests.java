package com.jsrxjt.adapter.demo;

import com.jsrxjt.common.core.constant.BusinessCode;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> Fengping
 * @description BusinessIdGenerator 完整测试类
 * @since 2025/6/4
 **/
@SpringBootTest
public class IdTests {

    @Autowired
    private BusinessIdGenerator businessIdGenerator;

    @Autowired
    private Environment env;

    // ========== generateId() 方法测试 ==========


    @Test
    public void testProfile() {
        String[] activeProfiles = env.getActiveProfiles();
        for (String activeProfile : activeProfiles) {
            System.out.println("active profile is " + activeProfile);
        }

    }

    @Test
    public void testGenerateId() {
        System.out.println("=== 测试基础ID生成 ===");
        Set<Long> generatedIds = new HashSet<>();

        for (int i = 0; i < 10; i++) {
            long id = businessIdGenerator.generateId();
            System.out.println("第" + (i + 1) + "个生成的ID为：" + id + " 字符长度为：" + String.valueOf(id).length());

            // 验证ID唯一性
            assert !generatedIds.contains(id) : "发现重复的ID: " + id;
            generatedIds.add(id);

            // 验证ID为正数
            assert id > 0 : "生成的ID应该为正数: " + id;
        }
        System.out.println("=== 基础ID生成测试完成 ===");
    }

    @Test
    public void testGenerateIdConcurrency() throws InterruptedException {
        System.out.println("=== 测试基础ID并发生成唯一性 ===");

        int threadCount = 10;
        int idsPerThread = 100;
        Set<Long> allIds = ConcurrentHashMap.newKeySet();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int t = 0; t < threadCount; t++) {
            final int threadNum = t;
            executor.submit(() -> {
                try {
                    for (int i = 0; i < idsPerThread; i++) {
                        long id = businessIdGenerator.generateId();
                        assert id > 0 : "线程" + threadNum + "生成的ID应该为正数: " + id;

                        boolean added = allIds.add(id);
                        assert added : "线程" + threadNum + "生成了重复ID: " + id;
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("并发测试完成，共生成 " + allIds.size() + " 个唯一ID");
        System.out.println("预期数量: " + (threadCount * idsPerThread));
        assert allIds.size() == threadCount * idsPerThread : "ID总数不匹配，存在重复!";
        System.out.println("=== 基础ID并发唯一性测试通过 ===");
    }

    // ========== generateDayBizId() 方法测试 ==========

    @Test
    public void testGenerateDayBizId() {
        System.out.println("=== 测试带日期前缀的业务ID生成 ===");
        Set<String> generatedIds = new HashSet<>();
        String externalId = "user123";
        String todayPrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        for (int i = 0; i < 10; i++) {
            String bizId = businessIdGenerator.generateDayBizId(BusinessCode.TRADE_ORDER, externalId);
            System.out.println("第" + (i + 1) + "个生成的日期业务ID为：" + bizId + " 长度为：" + bizId.length());

            // 验证ID唯一性
            assert !generatedIds.contains(bizId) : "发现重复的日期业务ID: " + bizId;
            generatedIds.add(bizId);

            // 验证ID格式：应该以今天的日期开头
            assert bizId.startsWith(todayPrefix) : "日期业务ID应该以今天日期开头: " + bizId;

            // 验证包含业务编码
            assert bizId.contains(BusinessCode.TRADE_ORDER.getCodeString()) : "日期业务ID应该包含业务编码: " + bizId;
        }
        System.out.println("=== 带日期前缀的业务ID生成测试完成 ===");
    }

    @Test
    public void testGenerateDayBizIdWithDifferentExternalIds() {
        System.out.println("=== 测试不同外部ID的日期业务ID生成 ===");
        String[] externalIds = { "user001", "user002", "order123", "product456" };

        for (String externalId : externalIds) {
            String bizId = businessIdGenerator.generateDayBizId(BusinessCode.TRADE_ORDER, externalId);
            System.out.println("外部ID: " + externalId + " -> 生成的日期业务ID: " + bizId);

            // 验证基本格式
            assert bizId != null && !bizId.isEmpty() : "生成的日期业务ID不能为空";
            assert bizId.length() > 8 : "日期业务ID长度应该大于8位（日期部分）";
        }
        System.out.println("=== 不同外部ID的日期业务ID生成测试完成 ===");
    }

    @Test
    public void testGenerateDayBizIdConcurrency() throws InterruptedException {
        System.out.println("=== 测试日期业务ID并发生成唯一性 ===");

        int threadCount = 5;
        int idsPerThread = 50;
        Set<String> allIds = ConcurrentHashMap.newKeySet();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int t = 0; t < threadCount; t++) {
            final int threadNum = t;
            executor.submit(() -> {
                try {
                    for (int i = 0; i < idsPerThread; i++) {
                        String bizId = businessIdGenerator.generateDayBizId(BusinessCode.TRADE_ORDER,
                                "user" + threadNum);
                        assert bizId != null && !bizId.isEmpty() : "线程" + threadNum + "生成的日期业务ID不能为空";

                        boolean added = allIds.add(bizId);
                        assert added : "线程" + threadNum + "生成了重复的日期业务ID: " + bizId;
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("日期业务ID并发测试完成，共生成 " + allIds.size() + " 个唯一ID");
        assert allIds.size() == threadCount * idsPerThread : "日期业务ID总数不匹配，存在重复!";
        System.out.println("=== 日期业务ID并发唯一性测试通过 ===");
    }

    // ========== generateBizId() 方法测试 ==========

    @Test
    public void testGenerateBizId() {
        System.out.println("=== 测试业务ID生成 ===");
        Set<String> generatedIds = new HashSet<>();
        String externalId = "user123";

        for (int i = 0; i < 10; i++) {
            String bizId = businessIdGenerator.generateBizId(BusinessCode.TRADE_ORDER, externalId);
            System.out.println("第" + (i + 1) + "个生成的业务ID为：" + bizId + " 长度为：" + bizId.length());

            // 验证ID唯一性
            assert !generatedIds.contains(bizId) : "发现重复的业务ID: " + bizId;
            generatedIds.add(bizId);

            // 验证包含业务编码
            assert bizId.contains(BusinessCode.TRADE_ORDER.getCodeString()) : "业务ID应该包含业务编码: " + bizId;

            // 验证ID不为空
            assert bizId != null && !bizId.isEmpty() : "生成的业务ID不能为空";
        }
        System.out.println("=== 业务ID生成测试完成 ===");
    }

    @Test
    public void testGenerateBizIdWithDifferentExternalIds() {
        System.out.println("=== 测试不同外部ID的业务ID生成 ===");
        String[] externalIds = { "user001", "user002", "order123", "product456", null, "" };

        for (String externalId : externalIds) {
            try {
                String bizId = businessIdGenerator.generateBizId(BusinessCode.TRADE_ORDER, externalId);
                System.out.println("外部ID: " + externalId + " -> 生成的业务ID: " + bizId);

                // 验证基本格式
                assert bizId != null && !bizId.isEmpty() : "生成的业务ID不能为空";
            } catch (Exception e) {
                System.out.println("外部ID: " + externalId + " -> 异常: " + e.getMessage());
                // 对于null或空字符串的外部ID，可能会抛出异常，这是正常的
            }
        }
        System.out.println("=== 不同外部ID的业务ID生成测试完成 ===");
    }

    @Test
    public void testGenerateBizIdConcurrency() throws InterruptedException {
        System.out.println("=== 测试业务ID并发生成唯一性 ===");

        int threadCount = 5;
        int idsPerThread = 50;
        Set<String> allIds = ConcurrentHashMap.newKeySet();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int t = 0; t < threadCount; t++) {
            final int threadNum = t;
            executor.submit(() -> {
                try {
                    for (int i = 0; i < idsPerThread; i++) {
                        String bizId = businessIdGenerator.generateBizId(BusinessCode.TRADE_ORDER, "user" + threadNum);
                        assert bizId != null && !bizId.isEmpty() : "线程" + threadNum + "生成的业务ID不能为空";

                        boolean added = allIds.add(bizId);
                        assert added : "线程" + threadNum + "生成了重复的业务ID: " + bizId;
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("业务ID并发测试完成，共生成 " + allIds.size() + " 个唯一ID");
        assert allIds.size() == threadCount * idsPerThread : "业务ID总数不匹配，存在重复!";
        System.out.println("=== 业务ID并发唯一性测试通过 ===");
    }

    // ========== generateFixed18DigitId() 方法测试 ==========

    @Test
    public void testGenerateFixed18DigitId() {
        System.out.println("=== 测试固定18位长度分布式ID生成 ===");
        Set<String> generatedIds = new HashSet<>();

        for (int i = 0; i < 20; i++) {
            String fixed18DigitId = businessIdGenerator.generateFixed18DigitId();

            System.out.println("第" + (i + 1) + "个:");
            System.out.println("  18位ID: " + fixed18DigitId + " (长度: " + fixed18DigitId.length() + ")");
            System.out.println("  ---");

            // 验证长度必须为18位
            assert fixed18DigitId.length() == 18 : "生成的ID长度不是18位!";

            // 验证ID唯一性
            assert !generatedIds.contains(fixed18DigitId) : "发现重复的18位ID: " + fixed18DigitId;
            generatedIds.add(fixed18DigitId);

            // 验证ID只包含数字
            assert fixed18DigitId.matches("\\d{18}") : "18位ID包含非数字字符: " + fixed18DigitId;
        }
        System.out.println("=== 测试完成，生成了" + generatedIds.size() + "个唯一的18位ID ===");
    }

    @Test
    public void testFixed18DigitIdConcurrency() throws InterruptedException {
        System.out.println("=== 测试18位ID并发生成唯一性 ===");

        int threadCount = 10;
        int idsPerThread = 100;
        Set<String> allIds = ConcurrentHashMap.newKeySet();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int t = 0; t < threadCount; t++) {
            final int threadNum = t;
            executor.submit(() -> {
                try {
                    for (int i = 0; i < idsPerThread; i++) {
                        String id = businessIdGenerator.generateFixed18DigitId();
                        assert id.length() == 18 : "线程" + threadNum + "生成的ID长度不是18位: " + id;
                        assert id.matches("\\d{18}") : "线程" + threadNum + "生成的ID包含非数字: " + id;

                        boolean added = allIds.add(id);
                        assert added : "线程" + threadNum + "生成了重复ID: " + id;
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("并发测试完成，共生成 " + allIds.size() + " 个唯一ID");
        System.out.println("预期数量: " + (threadCount * idsPerThread));
        assert allIds.size() == threadCount * idsPerThread : "ID总数不匹配，存在重复!";
        System.out.println("=== 并发唯一性测试通过 ===");
    }

    @Test
    public void testFixed18DigitIdTimeRelevance() {
        System.out.println("=== 测试18位ID时间相关性 ===");

        String id1 = businessIdGenerator.generateFixed18DigitId();

        try {
            Thread.sleep(1000); // 等待1秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String id2 = businessIdGenerator.generateFixed18DigitId();

        System.out.println("时间间隔1秒前的ID: " + id1);
        System.out.println("时间间隔1秒后的ID: " + id2);

        // 时间戳部分（前10位）应该有差异
        String timePart1 = id1.substring(0, 10);
        String timePart2 = id2.substring(0, 10);

        System.out.println("ID1时间部分: " + timePart1);
        System.out.println("ID2时间部分: " + timePart2);

        // 验证时间部分确实不同（反映了时间差异）
        assert !timePart1.equals(timePart2) : "时间间隔1秒的ID时间部分应该不同";

        System.out.println("=== 时间相关性测试通过 ===");
    }

    // ========== parserMilliseconds() 方法测试 ==========

    @Test
    public void testParserMilliseconds() {
        System.out.println("=== 测试业务ID时间戳解析 ===");

        // 生成一些业务ID并解析其时间戳
        String[] testBizIds = new String[5];
        long[] expectedTimestamps = new long[5];

        for (int i = 0; i < 5; i++) {
            // 生成日期业务ID
            String bizId = businessIdGenerator.generateDayBizId(BusinessCode.TRADE_ORDER, "user" + i);
            testBizIds[i] = bizId;
            expectedTimestamps[i] = System.currentTimeMillis();

            System.out.println("生成的业务ID: " + bizId);

            try {
                long parsedTimestamp = businessIdGenerator.parserBizIdMilliseconds(bizId);
                System.out.println("解析的时间戳: " + parsedTimestamp);
                System.out.println("解析的时间: " + new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                        .format(new java.util.Date(parsedTimestamp)));

                // 验证解析的时间戳应该接近当前时间（允许一定误差）
                long timeDiff = Math.abs(parsedTimestamp - expectedTimestamps[i]);
                assert timeDiff < 60000 : "解析的时间戳与预期时间差异过大: " + timeDiff + "ms";

            } catch (Exception e) {
                System.out.println("解析业务ID时间戳异常: " + e.getMessage());
                // 某些业务ID格式可能不支持时间戳解析，这是正常的
            }

            System.out.println("---");
        }
        System.out.println("=== 业务ID时间戳解析测试完成 ===");
    }

    @Test
    public void testParserMillisecondsWithInvalidIds() {
        System.out.println("=== 测试无效业务ID的时间戳解析 ===");

        String[] invalidBizIds = { "", "invalid", "123", "abc123def", null };

        for (String invalidId : invalidBizIds) {
            try {
                System.out.println("测试无效ID: " + invalidId);
                long timestamp = businessIdGenerator.parserBizIdMilliseconds(invalidId);
                System.out.println("解析结果: " + timestamp);

                // 对于无效ID，可能返回0或抛出异常
                if (timestamp == 0) {
                    System.out.println("无效ID返回时间戳0，符合预期");
                }

            } catch (Exception e) {
                System.out.println("无效ID抛出异常: " + e.getMessage() + "，符合预期");
            }
            System.out.println("---");
        }
        System.out.println("=== 无效业务ID时间戳解析测试完成 ===");
    }

    // ========== 辅助方法 ==========

    /**
     * 解析ID中的时间戳（辅助方法，用于测试验证）
     */
    public String parserMilliseconds(Long id) {
        long timestamp = (id >> 16) + 1749113400000L;
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(new java.util.Date(timestamp));
    }

    @Test
    public void testParserMillisecondsHelper() {
        System.out.println("=== 测试辅助解析方法 ===");
        long id = 3910761252883L; // 示例ID，可以根据实际情况替换
        String timestampStr = parserMilliseconds(id);
        System.out.println("时间戳对应的日期为：" + timestampStr);

        // 验证解析结果不为空
        assert timestampStr != null && !timestampStr.isEmpty() : "解析的时间字符串不能为空";
        System.out.println("=== 辅助解析方法测试完成 ===");
    }

    // ========== 综合测试 ==========

    @Test
    public void testAllMethodsIntegration() {
        System.out.println("=== 综合测试所有方法 ===");

        // 测试基础ID生成
        long baseId = businessIdGenerator.generateId();
        System.out.println("基础ID: " + baseId);
        assert baseId > 0 : "基础ID应该为正数";

        // 测试18位固定ID生成
        String fixed18Id = businessIdGenerator.generateFixed18DigitId();
        System.out.println("18位固定ID: " + fixed18Id);
        assert fixed18Id.length() == 18 : "18位ID长度应该为18";

        String customerId = String.valueOf(businessIdGenerator.generateId());

        // 测试业务ID生成
        String orderNo = businessIdGenerator.generateBizId(BusinessCode.TRADE_ORDER, customerId);
        System.out.println("业务ID: " + orderNo);
        assert orderNo != null && !orderNo.isEmpty() : "业务ID不能为空";

        // 测试日期业务ID生成
        String transactionNo =  businessIdGenerator.generateDayBizId(BusinessCode.TRADE_ORDER, customerId);
        System.out.println("日期业务ID: " + transactionNo);
        assert transactionNo != null && !transactionNo.isEmpty() : "日期业务ID不能为空";

        // 测试时间戳解析
        try {
            long timestamp = businessIdGenerator.parserBizIdMilliseconds(orderNo);
            System.out.println("解析的时间戳: " + timestamp);
            System.out.println("解析的时间: " + new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new java.util.Date(timestamp)));
        } catch (Exception e) {
            System.out.println("时间戳解析异常: " + e.getMessage());
        }

        System.out.println("=== 综合测试完成 ===");
    }

}
