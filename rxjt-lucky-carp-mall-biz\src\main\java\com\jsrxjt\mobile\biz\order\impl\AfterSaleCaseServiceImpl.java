package com.jsrxjt.mobile.biz.order.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.RefundAccountDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.TradeRefundInfoEntity;
import com.jsrxjt.mobile.domain.order.query.AfterSaleListQuery;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeRefundInfoRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.AfterSaleProgressBuilder;
import com.jsrxjt.mobile.domain.order.service.strategy.aftersale.impl.AfterSaleProgressBuilderFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 售后业务服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AfterSaleCaseServiceImpl implements AfterSaleCaseService {

    private final AfterSaleRepository afterSaleRepository;
    private final OrderRepository orderRepository;
    private final TradeRefundInfoRepository tradeRefundInfoRepository;
    private final AfterSaleProgressBuilderFactory afterSaleProgressBuilderFactory;

    @Override
    public AfterSaleDetailResponseDTO getAfterSaleDetail(String afterSaleNo, Long customerId) {
        log.info("开始查询售后详情，售后单号：{}，客户ID：{}", afterSaleNo, customerId);

        // 查询售后信息
        AfterSaleEntity afterSale = afterSaleRepository.findByAfterSaleNoAndCustomerId(afterSaleNo, customerId);
        if (afterSale == null) {
            log.warn("售后单不存在或无权限访问，售后单号：{}，客户ID：{}", afterSaleNo, customerId);
            throw new BizException("售后单不存在");
        }

        // 查询订单信息
        OrderInfoEntity order = orderRepository.findByOrderNo(afterSale.getOrderNo());
        if (order == null) {
            log.warn("关联订单不存在，订单号：{}", afterSale.getOrderNo());
            throw new BizException("关联订单不存在");
        }

        // 构建响应DTO
        AfterSaleDetailResponseDTO response = buildAfterSaleDetailResponse(afterSale, order);

        log.info("售后详情查询完成，售后单号：{}", afterSaleNo);
        return response;
    }

    /**
     * 构建售后详情响应
     */
    private AfterSaleDetailResponseDTO buildAfterSaleDetailResponse(AfterSaleEntity afterSale,
            OrderInfoEntity order) {
        AfterSaleDetailResponseDTO response = new AfterSaleDetailResponseDTO();

        // 设置售后状态
        response.setAfterSaleStatus(afterSale.getAfterSaleStatus());

        // 设置退款状态
        response.setRefundStatus(afterSale.getRefundStatus());

        // 设置售后类型
        response.setAfterSaleType(afterSale.getAfterSaleType());

        // 设置退款总金额
        response.setRefundTotalAmount(afterSale.getRefundAmount());

        // 设置是否显示删除按钮（隐藏）
        response.setCanDelete(afterSale.canDelete());



        // 设置退款详情信息
        if (RefundStatusEnum.REFUND_SUCCESS.getCode().equals(afterSale.getRefundStatus()) &&
                StringUtils.isNotBlank(afterSale.getRefundNo())) {
            // 如果退款状态是退款成功，则根据refundNo去trade_refund_info表查询退款信息
            List<TradeRefundInfoEntity> tradeRefundInfoList = tradeRefundInfoRepository
                    .findByOutRefundNo(afterSale.getRefundNo());
            if (!tradeRefundInfoList.isEmpty()) {
                List<RefundAccountDTO> refundAccountList = tradeRefundInfoList.stream()
                        .map(this::convertToRefundAccountDTO)
                        .collect(Collectors.toList());
                response.setRefundAccountList(refundAccountList);
            }
        }

        // 构建进度列表
        // response.setProgressList(buildProgressList(afterSale));

        // 构建进度详情
        response.setProgressDetails(buildProgressDetails(afterSale, order));

        // 构建退款信息
        response.setRefundInfo(buildRefundInfo(afterSale, order));

        return response;
    }

    /**
     * 构建进度列表
     */
    private List<AfterSaleDetailResponseDTO.AfterSaleProgressDTO> buildProgressList(AfterSaleEntity afterSale) {
        Integer currentStatus = afterSale.getAfterSaleStatus();
        List<AfterSaleDetailResponseDTO.AfterSaleProgressDTO> progressList = new ArrayList<>();

        // 提交申请
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step1 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step1.setStepName("提交申请");
        step1.setCompleted(true);
        step1.setCurrent(currentStatus.equals(AfterSaleStatusEnum.PENDING_AUDIT.getCode()));
        progressList.add(step1);
        if (Boolean.TRUE.equals(step1.getCurrent())) {
            return progressList; // 如果当前步骤是提交申请，则不需要继续构建其他步骤
        }
        if (currentStatus.equals(AfterSaleStatusEnum.AUDIT_REJECTED.getCode())
                || currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_CANCELLED.getCode())) {
            // 如果当前状态是审核驳回或撤销，则不需要继续构建其他步骤
            AfterSaleDetailResponseDTO.AfterSaleProgressDTO step2 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
            step2.setStepName(Objects.requireNonNull(AfterSaleStatusEnum.getByCode(currentStatus)).getDesc());
            step2.setCompleted(true);
            step2.setCurrent(true);
            progressList.add(step2);
            return progressList;
        }

        // 客服审核
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step2 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step2.setStepName("客服审核");
        step2.setCompleted(currentStatus >= AfterSaleStatusEnum.AUDIT_PASSED.getCode());
        step2.setCurrent(currentStatus.equals(AfterSaleStatusEnum.AUDIT_PASSED.getCode()));
        progressList.add(step2);
        Integer refundStatus = afterSale.getRefundStatus();

        // 处理中
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step3 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step3.setStepName("处理中");
        step3.setCompleted(currentStatus >= AfterSaleStatusEnum.AUDIT_PASSED.getCode());

        // 退款处理中
        step3.setCurrent(refundStatus.equals(RefundStatusEnum.REFUNDING.getCode()) ||
                refundStatus.equals(RefundStatusEnum.NOT_REFUNDED.getCode()));
        progressList.add(step3);

        // 已完成
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step4 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step4.setStepName("已完成");
        step4.setCompleted(currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()));
        step4.setCurrent(currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()));
        if (currentStatus > AfterSaleStatusEnum.AUDIT_PASSED.getCode()
                && currentStatus < AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()) {
            // 售后驳回的场景
            step4.setStepName("售后驳回");
            step4.setCompleted(true);
            step4.setCurrent(true);
        }
        progressList.add(step4);

        return progressList;
    }

    /**
     * 构建进度详情 - 根据售后状态和订单状态构建
     */
    private List<AfterSaleDetailResponseDTO.ProgressDetailDTO> buildProgressDetails(AfterSaleEntity afterSale,
            OrderInfoEntity order) {
        AfterSaleProgressBuilder builder = afterSaleProgressBuilderFactory.getBuilder(afterSale.getAfterSaleStatus());
        return builder.build(afterSale, order);
    }

    /**
     * 构建退款信息
     */
    private AfterSaleDetailResponseDTO.RefundInfoDTO buildRefundInfo(AfterSaleEntity afterSale, OrderInfoEntity order) {
        AfterSaleDetailResponseDTO.RefundInfoDTO refundInfo = new AfterSaleDetailResponseDTO.RefundInfoDTO();

        // 构建商品信息
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            OrderItemEntity orderItem = order.getOrderItems().get(0);
            AfterSaleDetailResponseDTO.RefundInfoDTO.ProductInfoDTO productInfo = new AfterSaleDetailResponseDTO.RefundInfoDTO.ProductInfoDTO();
            productInfo.setProductName(orderItem.getProductName());
            productInfo.setBrandName(orderItem.getBrandName());
            productInfo.setProductLogo(orderItem.getProductLogo());
            productInfo.setFaceAmount(orderItem.getFaceAmount());
            productInfo.setSellPrice(orderItem.getSellPrice());
            productInfo.setQuantity(orderItem.getQuantity());
            productInfo.setFlatProductType(orderItem.getFlatProductType());
            refundInfo.setProductInfo(productInfo);
        }

        // 设置退款信息
        refundInfo.setRefundAmount(afterSale.getRefundAmount());
        refundInfo.setApplyQuantity(afterSale.getAfterSaleQuantity());
        refundInfo.setApplyTime(afterSale.getApplyTime());
        refundInfo.setAfterSaleNo(afterSale.getAfterSaleNo());
        refundInfo.setOrderNo(afterSale.getOrderNo());
        // 退款原因 写入补充说明字段
        refundInfo.setRefundReason(afterSale.getRefundDescription());

        return refundInfo;
    }

    @Override
    public PageDTO<AfterSaleListResponseDTO> pageAfterSaleList(Long customerId, AfterSaleListRequestDTO request) {
        log.info("开始查询用户售后单列表，客户ID：{}，售后状态：{}，售后类型“{}",
                customerId, request.getAfterSaleStatus(), request.getAfterSaleType());

        // 时间字符串转换
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        if (StringUtils.isNotBlank(request.getCreateTimeStart())) {
            try {
                LocalDateTime startDate = LocalDateTimeUtil.parse(request.getCreateTimeStart(), "yyyy-MM-dd");
                startTime = LocalDateTimeUtil.beginOfDay(startDate);
            } catch (Exception e) {
                log.warn("售后申请开始时间格式错误：{}", request.getCreateTimeStart());
                throw new BizException("售后申请开始时间格式错误，请使用 yyyy-MM-dd格式");
            }
        }

        if (StringUtils.isNotBlank(request.getCreateTimeEnd())) {
            try {
                LocalDateTime endDate = LocalDateTimeUtil.parse(request.getCreateTimeEnd(), "yyyy-MM-dd");
                endTime = LocalDateTimeUtil.endOfDay(endDate);
            } catch (Exception e) {
                log.warn("售后申请结束时间格式错误：{}", request.getCreateTimeEnd());
                throw new BizException("售后申请结束时间格式错误，请使用 yyyy-MM-dd格式");
            }
        }

        // 关键字搜索处理：判断是否为数字（订单号/售后单号）还是文本（商品名称）
        String orderNo = null;
        String afterSaleNo = null;
        String productName = null;

        if (StringUtils.isNotBlank(request.getKeyword())) {
            String keyword = request.getKeyword().trim();
            // 如果关键字是纯数字，可能是订单号或售后单号
            if (NumberUtil.isNumber(keyword)) {
                // 如果以特定前缀开头，判断为售后单号，否则判断为订单号
                if (keyword.startsWith("20")) {
                    afterSaleNo = keyword;
                } else {
                    orderNo = keyword;
                }
            } else {
                // 非数字关键字作为商品名称搜索
                productName = keyword;
            }
        }

        // 构建查询条件
        AfterSaleListQuery query = AfterSaleListQuery.builder()
                .customerId(customerId)
                .afterSaleStatus(request.getAfterSaleStatus() < 0 ? null : request.getAfterSaleStatus())
                .afterSaleType(request.getAfterSaleType())
                .createTimeStart(startTime)
                .createTimeEnd(endTime)
                .firstCategoryId(request.getFirstCategoryId())
                .orderNo(orderNo)
                .afterSaleNo(afterSaleNo)
                .productName(productName)
                .isShow(true)
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();

        // 查询售后单列表
        PageDTO<AfterSaleEntity> afterSalePage = afterSaleRepository.findAfterSaleListByPage(query);

        // 转换为响应DTO
        List<AfterSaleListResponseDTO> afterSaleList = afterSalePage.getRecords().stream()
                .map(this::convertToAfterSaleListResponse)
                .toList();

        return PageDTO.<AfterSaleListResponseDTO>builder()
                .records(afterSaleList)
                .total(afterSalePage.getTotal())
                .current(afterSalePage.getCurrent())
                .size(afterSalePage.getSize())
                .pages(afterSalePage.getPages())
                .build();
    }

    @Override
    public AutoRefundRequestDTO calDistributionOrderRefundAmount(AutoRefundRequestDTO refundRequestDTO) {
        OrderInfoEntity order = orderRepository.findByOrderNo(refundRequestDTO.getOrderNo());
        // 第三方退款金额
        BigDecimal externalRefundAmount = refundRequestDTO.getExternalRefundAmount();
        // 订单总金额
        BigDecimal orderAmount = order.getOrderAmount();
        // 第三方订单总金额
        BigDecimal totalSellAmount = order.getTotalSellAmount();
        // 加点手续费 + 超额手续费
        BigDecimal extraAmount = orderAmount.subtract(totalSellAmount);
        // 查询订单已发生退款的第三方订单总金额
        List<AfterSaleEntity> refundedAfterSales = afterSaleRepository.findValidAfterSalesByOrderNo(order.getOrderNo());
        log.info("已发生的售后订单记录为:{}", JSONUtil.toJsonStr(refundedAfterSales));
        BigDecimal refundedExternalSumAmount = refundedAfterSales.stream()
                .map(AfterSaleEntity::getExternalRefundAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 已退款金额 + 本次退款金额之和是否等于第三方订单总金额
        BigDecimal totalExternalRefundAmount = refundedExternalSumAmount.add(externalRefundAmount);
        boolean fullRefundFlag = totalExternalRefundAmount.compareTo(totalSellAmount) == 0;
        // 当前是最后一次退款
        if (fullRefundFlag) {

            // 福鲤圈侧已退款的订单总金额
            BigDecimal refundedSumAmount = refundedAfterSales.stream()
                    .map(AfterSaleEntity::getRefundAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 最后一次剩余退款金额
            BigDecimal leftRefundAmount = orderAmount.subtract(refundedSumAmount);
            log.info("订单:{},本次是最后一次退款,已退款金额:{},剩余退款金额:{}", order.getOrderNo(), totalExternalRefundAmount,
                    leftRefundAmount);
            refundRequestDTO.setApplyRefundAmount(leftRefundAmount);
        } else {
            // 分摊手续费计算 -- 本次第三方退款金额/第三方订单总金额 * 总手续费
            BigDecimal subExtraAmount = externalRefundAmount.multiply(extraAmount).divide(totalSellAmount, 2,
                    RoundingMode.HALF_UP);
            log.info("订单:{},本次不是最后一次退款,退款金额:{},退款手续费金额:{}", order.getOrderNo(),
                    externalRefundAmount.add(subExtraAmount), subExtraAmount);
            refundRequestDTO.setApplyRefundAmount(externalRefundAmount.add(subExtraAmount));
        }
        return refundRequestDTO;
    }

    /**
     * 转换为售后单列表响应DTO
     */
    private AfterSaleListResponseDTO convertToAfterSaleListResponse(AfterSaleEntity entity) {
        AfterSaleListResponseDTO response = new AfterSaleListResponseDTO();

        response.setAfterSaleNo(entity.getAfterSaleNo());
        response.setOrderNo(entity.getOrderNo());
        response.setAfterSaleType(entity.getAfterSaleType());
        response.setAfterSaleStatus(entity.getAfterSaleStatus());
        response.setRefundStatus(entity.getRefundStatus());
        response.setRefundAmount(entity.getRefundAmount());
        response.setApplyQuantity(entity.getAfterSaleQuantity());
        if (entity.getOrderItem() != null) {
            response.setProductName(entity.getOrderItem().getProductName());
            response.setBrandName(entity.getOrderItem().getBrandName());
            response.setProductLogo(entity.getOrderItem().getProductLogo());
            response.setFaceAmount(entity.getOrderItem().getFaceAmount());
            response.setSellPrice(entity.getOrderItem().getSellPrice());
            response.setQuantity(entity.getOrderItem().getQuantity());
            response.setFlatProductType(entity.getOrderItem().getFlatProductType());
        }
        response.setCreateTime(entity.getCreateTime());

        return response;
    }

    /**
     * 将TradeRefundInfoEntity转换为RefundAccountDTO
     *
     * @param tradeRefundInfo 交易退款信息实体
     * @return 退款账户DTO
     */
    private RefundAccountDTO convertToRefundAccountDTO(TradeRefundInfoEntity tradeRefundInfo) {
        RefundAccountDTO refundAccountDTO = new RefundAccountDTO();

        // accountType 对应 card_trade_type
        refundAccountDTO.setAccountType(tradeRefundInfo.getCardTradeType());

        // accountNo 对应 card_no
        refundAccountDTO.setAccountNo(tradeRefundInfo.getCardNo());

        // refundAmount要改成元
        refundAccountDTO.setRefundAmount(tradeRefundInfo.getRefundAmountInYuan());

        return refundAccountDTO;
    }

    @Override
    public void markAfterSaleAsHidden(String afterSaleNo, Long customerId) {
        log.info("开始删除售后单，售后单号：{}，客户ID：{}", afterSaleNo, customerId);

        // 1. 查询售后信息
        AfterSaleEntity afterSale = afterSaleRepository.findByAfterSaleNoAndCustomerId(afterSaleNo, customerId);
        if (afterSale == null) {
            log.warn("售后单不存在或无权限访问，售后单号：{}，客户ID：{}", afterSaleNo, customerId);
            throw new BizException("售后单不存在");
        }

        // 2. 校验售后状态是否可以删除
        validateAfterSaleCanDelete(afterSale);

        // 3. 设置售后单为不显示状态
        AfterSaleEntity updateAfterSale = new AfterSaleEntity();
        updateAfterSale.setId(afterSale.getId());
        updateAfterSale.setCustomerId(afterSale.getCustomerId());
        updateAfterSale.setHidden(); // 设置为不显示（隐藏）
        updateAfterSale.setModTime(LocalDateTime.now());

        afterSaleRepository.update(updateAfterSale);

        log.info("售后单删除成功，售后单号：{}，客户ID：{}", afterSaleNo, customerId);
    }

    /**
     * 校验售后单是否可以删除
     * 待审核和审核通过状态不可删除
     */
    private void validateAfterSaleCanDelete(AfterSaleEntity afterSale) {
        Integer afterSaleStatus = afterSale.getAfterSaleStatus();

        // 待审核状态不可删除
        if (AfterSaleStatusEnum.PENDING_AUDIT.getCode().equals(afterSaleStatus)) {
            throw new BizException("待审核的售后单不可删除");
        }

        // 审核通过状态不可删除
        if (AfterSaleStatusEnum.AUDIT_PASSED.getCode().equals(afterSaleStatus)) {
            throw new BizException("退款处理中的售后单不可删除");
        }

        log.debug("售后单状态校验通过，可以删除，售后单号：{}，状态：{}",
                afterSale.getAfterSaleNo(), afterSaleStatus);
    }
}
