package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.AutoRefundRequestDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.order.AutoRefundCaseService;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.alipay.gateway.AlipayVoucherGateway;
import com.jsrxjt.mobile.domain.alipay.request.AlipayVoucherSendRequest;
import com.jsrxjt.mobile.domain.alipay.response.AlipayVoucherSendResponse;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.customer.dp.RechargeAccount;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderDeliveryEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * 支付宝红包支付成功处理策略（基于flatProductType）
 * 支持 flatProductType = 402 的类型
 *
 * <AUTHOR>
 * @Date 2025/8/5
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AlipayVoucherPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;

    private final OrderDeliveryRepository orderDeliveryRepository;

    private final AlipayVoucherGateway alipayVoucherGateway;

    private final AutoRefundCaseService autoRefundCaseService;

    private final RechargeAccountHistoryService rechargeAccountHistoryService;

    private final AppCouponGoodsRepository appCouponGoodsRepository;


    @Override
    public boolean supports(Integer flatProductType) {
        // 仅支持 flatProductType == 402
        return flatProductType != null && flatProductType == 402;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理支付宝红包订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());

        // 1. 支付宝下单
        AlipayVoucherSendResponse alipayVoucherSendResponse = pushAlipayVoucherOrder(order);
        // 2. 下单成功-更新订单状态为已发货，并记录外部订单号
        //    下单失败-订单标记为充值中，保存异常状态到发货表，20分钟后再查询一次；
        //            订单标记为充值失败，保存异常状态到发货表，自动生成售后订单；
        if ( alipayVoucherSendResponse == null ){
            log.info("支付宝红包发放失败，订单号：{}", order.getOrderNo());
            updateOrderToRechargeFailed(order);
            createAfterSaleForRechargeFailed(order);
        } else {
            if (StringUtils.isNotEmpty(alipayVoucherSendResponse.getOutVoucherId())){
                log.info("支付宝红包发放成功，订单号：{}，外部订单号：{}", order.getOrderNo(), alipayVoucherSendResponse.getOutOrderId());
                updateOrderToDelivered(order, alipayVoucherSendResponse.getOutOrderId());
            } else{
                if (alipayVoucherSendResponse.getNeedRetry()){
                    updateOrderToRechargeDelivering(order);
                } else {
                    updateOrderToRechargeFailed(order);
                    createAfterSaleForRechargeFailed(order);
                }
            }
        }
        saveDeliveryInfo(order, alipayVoucherSendResponse);
        // 更新SKU销量
        updateSkuSoldNum(order);
        log.info("支付宝红包订单支付成功处理完成，订单号：{}", order.getOrderNo());
    }

    /**
     * 支付宝下单发券
     */
    private AlipayVoucherSendResponse pushAlipayVoucherOrder(OrderInfoEntity order) {
        AlipayVoucherSendRequest alipayVoucherSendRequest = new AlipayVoucherSendRequest();
        alipayVoucherSendRequest.setOrderNo(order.getOrderNo());
        alipayVoucherSendRequest.setOutActivityId(order.getOrderItems().get(0).getOutGoodsId());
        alipayVoucherSendRequest.setLogonId(order.getRechargeAccount());
        return alipayVoucherGateway.sendAlipayVoucher(alipayVoucherSendRequest);
    }

    /**
     * 更新订单状态为交易完成  发货状态为已发货
     */
    private void updateOrderToDelivered(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("支付宝红包订单发货状态更新成功，订单号：{}，发货状态：{}，外部订单号：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription(), externalOrderNo);
        // 保存充值记录
        RechargeAccount rechargeAccount = new RechargeAccount(order.getCustomerId(),order.getProductSpuId(),
                ProductTypeEnum.COUPON_APP.getType(),order.getRechargeAccountType(),order.getRechargeAccount());
        rechargeAccountHistoryService.save(rechargeAccount);
    }

    /**
     * 保存发货信息到t_order_delivery表
     */
    private void saveDeliveryInfo(OrderInfoEntity orderInfo,AlipayVoucherSendResponse alipayVoucherSendResponse) {
        log.info("[数据保存]开始保存支付宝红包发货信息，订单号：{}", orderInfo.getOrderNo());

        // 订单项记录
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);
        OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();
        // 基本订单信息
        deliveryEntity.setOrderId(orderInfo.getId());
        deliveryEntity.setOrderNo(orderInfo.getOrderNo());
        deliveryEntity.setOrderItemId(orderItem.getId());
        deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());
        deliveryEntity.setDeliveryType(3);
        deliveryEntity.setDeliveryTime(LocalDateTime.now());
        // 发货信息
        if (alipayVoucherSendResponse != null){
            if (alipayVoucherSendResponse.getOutOrderId() != null){
                deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
                // 券信息
                deliveryEntity.setCouponCode(alipayVoucherSendResponse.getOutVoucherId());
            } else {
                deliveryEntity.setDeliveryErrorCode(alipayVoucherSendResponse.getSubCode());
                deliveryEntity.setDeliveryErrorMsg(alipayVoucherSendResponse.getSubMsg());
                if (alipayVoucherSendResponse.getNeedRetry()){
                    deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
                } else {
                    deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
                }
            }
        } else {
            deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
            deliveryEntity.setDeliveryErrorMsg("接口请求异常");
        }
        // 收货信息（从订单信息复制）
        deliveryEntity.setReceiverName(orderInfo.getReceiverName());
        deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
        deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());
        deliveryEntity.setRechargeAccount(orderInfo.getRechargeAccount());

        // 发货备注
        deliveryEntity.setDeliveryRemark("红包自动发放");
        try {
            orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
            log.info("[数据保存]支付宝红包发货信息保存成功，订单号：{}", orderInfo.getOrderNo());
        } catch (Exception e) {
            log.error("[数据保存]支付宝红包发货信息保存失败，订单号：{}，错误信息：{}", orderInfo.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新订单状态为发货失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);

        log.info("订单发货状态更新为发货失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 更新订单状态为发货中，等待定时任务重试
     */
    private void updateOrderToRechargeDelivering(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("订单发货状态更新为发货中，等待定时任务重试，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERING.getDescription());
    }

    /**
     * 发放失败时自动创建售后单并退款
     *
     * @param orderInfo 订单信息
     */
    private void createAfterSaleForRechargeFailed(OrderInfoEntity orderInfo) {
        log.info("开始为支付宝红包发放失败订单创建售后单并自动退款，订单号：{}", orderInfo.getOrderNo());
        AutoRefundRequestDTO autoRefundRequestDTO = new AutoRefundRequestDTO();
        autoRefundRequestDTO.setOrderNo(orderInfo.getOrderNo());
        autoRefundRequestDTO.setApplyRefundAmount(orderInfo.getPaymentAmount());
        autoRefundRequestDTO.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode());
        AfterSaleEntity afterSaleEntity = autoRefundCaseService.autoRefund(autoRefundRequestDTO);
        log.info ("支付宝红包发放失败售后单退款成功，订单号：{}，售后单号：{}", orderInfo.getOrderNo(), afterSaleEntity.getAfterSaleNo());
    }

    /**
     * 更新卡券SKU销量
     */
    private void updateSkuSoldNum(OrderInfoEntity order) {
        try {
            // 获取订单中的商品SKU ID和数量
            Long productSkuId = order.getProductSkuId();
            Integer quantity = 1; // 默认数量为1

            // 如果订单有订单项，从订单项中获取数量
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                OrderItemEntity orderItem = order.getOrderItems().get(0);
                quantity = orderItem.getQuantity();
                // 如果订单主表的productSkuId为空，从订单项中获取
                if (productSkuId == null) {
                    productSkuId = orderItem.getProductSkuId();
                }
            }

            if (productSkuId == null) {
                log.warn("订单商品SKU ID为空，无法更新销量，订单号：{}", order.getOrderNo());
                return;
            }

            if (quantity == null || quantity <= 0) {
                log.warn("订单商品数量无效，无法更新销量，订单号：{}，数量：{}", order.getOrderNo(), quantity);
                return;
            }

            // 使用原子操作直接增加销量，避免并发问题
            int updateResult = appCouponGoodsRepository.increaseSoldNum(productSkuId, quantity);
            if (updateResult > 0) {
                log.info("支付宝SKU销量更新成功，SKU ID：{}，增加数量：{}，订单号：{}",
                        productSkuId, quantity, order.getOrderNo());
            } else {
                log.warn("支付宝SKU销量更新失败，可能SKU不存在，SKU ID：{}，订单号：{}", productSkuId, order.getOrderNo());
            }

        } catch (Exception e) {
            log.error("更新支付宝SKU销量异常，订单号：{}，错误信息：{}", order.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}