package com.jsrxjt.adapter.distribution.mq.consumer;

import cn.hutool.core.lang.UUID;
import com.jsrxjt.common.core.util.SimpleUniqueMacIdGenerator;
import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;


/**
 * 装修页面消息消费者配置
 * <AUTHOR>
 * @date 2025/08/26
 */
@Configuration
@RefreshScope
public class ScanPayWssConsumerConfig {


  @SneakyThrows
  @Bean(destroyMethod = "close")
  public PushConsumer scanPayWssConsumer(ClientConfiguration clientConfiguration,
    ScanPayWssMessageListener scanPayWssMessageListener) {
    FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
    String uniqueConsumerGroup = "scan_pay_wss_broadcast_" + SimpleUniqueMacIdGenerator.generateMachineCode();
    String topic = "scan_pay_wss_topic";
    return ClientServiceProvider.loadService().newPushConsumerBuilder()
        .setClientConfiguration(clientConfiguration)
        .setConsumerGroup(uniqueConsumerGroup)
        .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
        .setMessageListener(scanPayWssMessageListener)
        .build();
  }
}