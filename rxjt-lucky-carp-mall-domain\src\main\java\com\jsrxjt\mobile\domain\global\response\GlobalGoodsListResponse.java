package com.jsrxjt.mobile.domain.global.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 全球购活动商品列表
 * <AUTHOR>
 * @date 2025/06/24
 */
@Data
public class GlobalGoodsListResponse {

    private String productId;

    private String productItemId;

    private String imgUrl;

    private String productName;

    private String productSubtitle;

    private BigDecimal originalPrice;

    private BigDecimal price;

    private List<String> promotionLabels;

    private String activityImgUrl;

    private String activityLabel;
}
