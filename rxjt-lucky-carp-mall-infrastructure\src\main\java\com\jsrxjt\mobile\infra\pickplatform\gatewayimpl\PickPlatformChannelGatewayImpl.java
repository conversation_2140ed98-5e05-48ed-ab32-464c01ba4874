package com.jsrxjt.mobile.infra.pickplatform.gatewayimpl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformChannelGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayResultRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayCodeResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformShopDataResponse;
import com.jsrxjt.mobile.infra.pickplatform.config.PickPlatformConfig;
import com.jsrxjt.mobile.infra.pickplatform.util.RxMemberSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 提货券分销中台的渠道对接
 * @Author: ywt
 * @Date: 2025-05-19 09:33
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PickPlatformChannelGatewayImpl implements PickPlatformChannelGateway {
    protected static final String CODE = "code";
    protected static final String DATA = "data";
    protected static final Integer SUCCESS_CODE = 0;
    protected static final String MESSAGE_KEY = "message";
    protected final HttpClientGateway httpClientGateway;
    private final PickPlatformConfig pickPlatformConfig;

    @Override
    public PickPlatformPayCodeResponse getPaymentCode(PickPlatformPayCodeRequest request) {
        PickPlatformPayCodeResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", request.getNonce());
        requestMap.put("timestamp", request.getTimestamp());

        requestMap.put("third_id", request.getThirdId());
        requestMap.put("user_code", request.getUserCode());
        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            log.info("提货分销中台-获取付款码请求参数: {}", requestMap);
            String payCodeRes = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getPaymentCode(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(payCodeRes);
            log.info("提货分销中台-获取付款码返回参数: {}", payCodeRes);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get(DATA)), PickPlatformPayCodeResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-获取付款码接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    @Override
    public PickPlatformPayResultResponse getPaymentOrderResult(PickPlatformPayResultRequest request) {
        PickPlatformPayResultResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", IdUtil.simpleUUID());
        requestMap.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        requestMap.put("third_id", request.getThirdId());
        requestMap.put("order_no", request.getOrderNo());
        requestMap.put("type", request.getType());
        requestMap.put("trade_no", request.getTradeNo());
        requestMap.put("trade_time", request.getTradeTime());

        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            log.info("提货分销中台-订单支付结果回告请求参数: {}", requestMap);
            String payCodeRes = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getPayCallback(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(payCodeRes);
            log.info("提货分销中台-订单支付结果回告返回参数: {}", payCodeRes);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get(DATA)), PickPlatformPayResultResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-订单支付回告接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    /**
     * 订单退款结果回告[幂等性]
     *
     * @param request
     * @return
     */
    @Override
    public PickPlatformPayResultResponse getPaymentOrderRefund(PickPlatformPayRefundRequest request) {
        PickPlatformPayResultResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("refund_no", request.getRefundNo());
        requestMap.put("nonce", IdUtil.simpleUUID());
        requestMap.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));
        requestMap.put("third_id", request.getThirdId());
        requestMap.put("order_no", request.getOrderNo());
        requestMap.put("type", request.getType());
        requestMap.put("trade_no", request.getTradeNo());
        requestMap.put("trade_time", request.getTradeTime());

        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            log.info("提货分销中台-订单退款结果回告请求参数: {}", requestMap);
            String payCodeRes = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getRefundCallback(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(payCodeRes);
            log.info("提货分销中台-订单退款结果回告返回参数: {}", payCodeRes);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get(DATA)), PickPlatformPayResultResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-订单退款回告接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }


}
