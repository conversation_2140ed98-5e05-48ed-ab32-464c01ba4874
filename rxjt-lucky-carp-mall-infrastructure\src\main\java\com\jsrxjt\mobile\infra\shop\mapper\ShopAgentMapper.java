package com.jsrxjt.mobile.infra.shop.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.shop.po.ShopAgentPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @<PERSON> zhou_jj
 * @Date 2025/9/15
 */
@Mapper
public interface ShopAgentMapper extends CommonBaseMapper<ShopAgentPO> {
    List<ShopAgentPO> getScreenListByRegionId(@Param("adcodeList") List<String> adcodeList, @Param("lat") String lat, @Param("lng") String lng);
}
