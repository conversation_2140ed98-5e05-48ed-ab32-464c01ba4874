package com.jsrxjt.mobile.api.customer.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Data
public class CustomerCardRechargeRequest {

    @Schema(description = "充值订单来源来源 APP_ANDROID、APP_IOS、WX_MINI")
    @NotBlank(message = "充值订单来源不能为空")
    private String source;

    @Schema(description = "充值类型  RW白金卡,RB黑金卡")
    @NotBlank(message = "充值类型不能为空")
    private String rechargeType;

    @Schema(description = "充值卡号")
    @NotBlank(message = "充值卡号不能为空")
    private String rechargeCard;

    @Schema(description = "支付类型 RW,RB,RBIZ,WX")
    @NotBlank(message = "支付类型 RW,RB,RBIZ,WX")
    private String payType;

    @Schema(description = "充值充值金额 红卡、微信充值的时候必填")
    @DecimalMin(value = "1", message = "充值金额必须大于0")
    @Digits(integer = 10, fraction = 0, message = "充值金额必须是正整数")
    private BigDecimal rechargeAmount;

    @Schema(description = "支付卡卡号 卡充值必填")
    private String tradeCard;

    @Schema(description = "支付卡卡密 卡充值必填")
    private String tradeCardPwd;

    @Schema(description = "支付卡CVV 卡充值必填")
    private String tradeCardCvv;



}
