package com.jsrxjt.mobile.biz.order.coupon.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponNotifyResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponPlatformRechargeStatus;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.CouponOrderCreatedNotifyDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.coupon.CouponOrderCaseService;
import com.jsrxjt.mobile.domain.coupon.entity.CouponCardEntity;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.customer.dp.RechargeAccount;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 卡券订单处理服务实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/7/8
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class CouponOrderCaseServiceImpl implements CouponOrderCaseService {

    private final OrderRepository orderRepository;
    private final OrderDeliveryRepository orderDeliveryRepository;
    private final DistributedLock distributedLock;
    private final PlatformTransactionManager transactionManager;
    private final AfterSaleService afterSaleService;
    private final AfterSaleLogService afterSaleLogService;
    private final RechargeAccountHistoryService rechargeAccountHistoryService;
    private final AfterSaleRepository afterSaleRepository;
    private final AfterSaleLogRepository afterSaleLogRepository;

    private static final String COUPON_ORDER_LOCK_PREFIX = "coupon_order_notify_lock:";

    @Override
    public CouponNotifyResponseDTO processCouponOrderCreatedNotify(CouponOrderCreatedNotifyDTO notifyDTO) {
        log.info("[入口参数]处理卡券订单已被创建的通知，入参：{}", notifyDTO.getRequestBody());
        String outOrderSn = notifyDTO.getOutOrderSn();
        String lockKey = COUPON_ORDER_LOCK_PREFIX + outOrderSn;

        // 尝试加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey);
            if (!lockAcquired) {
                log.error("获取卡券订单通知处理锁失败，订单号：{}", outOrderSn);
                return CouponNotifyResponseDTO.fail("系统繁忙，请稍后重试");
            }

            // 验签，暂时不实现，暂时认为每次都成功
            log.info("[验签检查]卡券订单通知验签检查通过，订单号：{}", outOrderSn);

            // 根据产品类型区分处理逻辑
            if (notifyDTO.getProductType() != null && notifyDTO.getProductType() == 2) {
                // 套餐类型处理
                return processPackageSubOrderNotify(notifyDTO, outOrderSn);
            } else {
                // 单个卡券类型处理（原有逻辑）
                return processSingleCouponOrderNotify(notifyDTO, outOrderSn);
            }

        }  catch (BizException e) {
            log.error("处理卡券订单通知业务异常，订单号：{}，错误信息：{}", outOrderSn, e.getMsg(), e);
            return CouponNotifyResponseDTO.fail(e.getMsg());
        } catch (Exception e) {
            log.error("处理卡券订单通知异常，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            return CouponNotifyResponseDTO.fail("系统异常：" + e.getMessage());
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    private  void validateSign(CouponOrderCreatedNotifyDTO notifyDTO, String outOrderSn, OrderInfoEntity orderInfo) {
        Map<String,Object> parameters = notifyDTO.getRequestBody();
        String sign = CouponPlatformFactory.getCouponPlatform(
                CouponTypeEnum.getByType(orderInfo.getFlatProductType() % 100))
                .getParametersSign(parameters);
        if (sign.isEmpty() || !sign.equals(parameters.get("sign"))) {
            log.error("卡券订单通知验签失败，订单号：{} 回调的签名是{},计算得到的签名是{}", outOrderSn,parameters.get("sign"), sign);
            throw new BizException("验签失败");
        }
    }

    /**
     * 校验订单信息
     * 
     * @param notifyDTO  通知DTO
     * @param outOrderSn 外部订单号
     * @return 订单信息
     * @throws BizException 校验失败时抛出异常
     */
    private OrderInfoEntity validateOrderInfo(CouponOrderCreatedNotifyDTO notifyDTO, String outOrderSn) {
        // 根据订单号outOrderSn查找订单信息
        log.info("[数据读取]根据订单号查找订单信息，订单号：{}", outOrderSn);
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(outOrderSn);
        if (orderInfo == null) {
            log.error("订单不存在，订单号：{}", outOrderSn);
            throw new BizException("订单不存在");
        }

        // 对比externalOrderNo 和 orderSn参数是否一致
        if (!notifyDTO.getOrderSn().equals(orderInfo.getExternalOrderNo())) {
            log.error("外部订单号不一致，订单号：{}，通知中的orderSn：{}，订单中的externalOrderNo：{}",
                    outOrderSn, notifyDTO.getOrderSn(), orderInfo.getExternalOrderNo());
            throw new BizException("订单信息不匹配");
        }

        return orderInfo;
    }

    /**
     * 判断订单是否需要处理发货
     * 
     * @param orderInfo  订单信息
     * @param outOrderSn 外部订单号
     * @return true-需要处理发货，false-不需要处理
     */
    private boolean needProcessDelivery(OrderInfoEntity orderInfo, String outOrderSn) {
        // 验证订单的发货状态是否是待发货 或 发货中 不是则直接返回成功，提前结束
        Integer deliveryStatus = orderInfo.getDeliveryStatus();
        if (!DeliveryStatusEnum.UNDELIVERED.getCode().equals(deliveryStatus.byteValue())
                && !DeliveryStatusEnum.DELIVERING.getCode().equals(deliveryStatus.byteValue())) {
            log.info("订单发货状态不是待发货或发货中，跳过处理，订单号：{}，当前发货状态：{}",
                    outOrderSn, DeliveryStatusEnum.getDescriptionByCode(deliveryStatus.byteValue()));
            return false;
        }
        return true;
    }

    /**
     * 获取卡券信息（包含卡券类型识别和平台调用）
     * 
     * @param orderInfo  订单信息
     * @param outOrderSn 外部订单号
     * @return 卡券信息列表
     */
    private List<CouponCardEntity> obtainCouponCards(OrderInfoEntity orderInfo, String outOrderSn) {
        log.info("[业务逻辑]开始调用获卡接口发卡，订单号：{}，扁平化产品类型：{}",
                outOrderSn, orderInfo.getFlatProductType());

        // 根据flatProductType mod 100 得到具体卡券的类型
        int couponTypeCode = orderInfo.getFlatProductType() % 100;
        CouponTypeEnum couponType = CouponTypeEnum.getByType(couponTypeCode);

        if (couponType == null) {
            log.error("无法识别的卡券类型，订单号：{}，扁平化产品类型：{}，卡券类型码：{}",
                    outOrderSn, orderInfo.getFlatProductType(), couponTypeCode);
            throw new BizException("无法识别的卡券类型");
        }

        log.info("[第三方服务调用]获取卡券平台策略，卡券类型：{}", couponType.getText());
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);

        // 调用获卡接口获取卡券信息
        return getCouponCards(couponPlatform, orderInfo.getExternalOrderNo(), outOrderSn);
    }

    /**
     * 更新订单和发货状态（手动提交事务）
     * 
     * @param orderInfo   订单信息
     * @param couponCards 卡券信息列表
     * @param outOrderSn  外部订单号
     */
    private void updateOrderAndDeliveryStatus(OrderInfoEntity orderInfo, List<CouponCardEntity> couponCards,
            String outOrderSn) {
        // 发卡成功 更新订单发货状态为已发货，记录卡券信息到t_order_delivery表
        log.info("[数据保存]发卡成功，更新订单发货状态为已发货，订单号：{}，获取到{}张卡券",
                outOrderSn, couponCards.size());

        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新订单状态
            updateOrderToDelivered(orderInfo);

            // 保存发货信息
            saveCouponDeliveryInfo(orderInfo, couponCards);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("卡券信息已保存到发货表，订单号：{}，卡券数量：{}", outOrderSn, couponCards.size());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("更新订单和发货状态失败，事务已回滚，订单号：{}，错误信息：{}", outOrderSn, e.getMessage(), e);
            throw new BizException("更新订单和发货状态失败：" + e.getMessage());
        }
    }

    /**
     * 调用卡券平台获取卡券信息
     * 
     * @param couponPlatform 卡券平台策略
     * @param externalOrderNo 卡券平台订单号
     * @param orderNo     订单号
     * @return 卡券信息列表
     */
    private List<CouponCardEntity> getCouponCards(CouponPlatformStrategy couponPlatform,
            String externalOrderNo,
            String orderNo) {
        log.info("[第三方服务调用]调用获卡接口，卡管订单号：{} 福鲤圈订单号{}", externalOrderNo, orderNo);
        List<CouponCardEntity> couponCards = couponPlatform.getCouponCardInfo(externalOrderNo);

        if (couponCards == null || couponCards.isEmpty()) {
            log.error("获卡失败，未获取到卡券信息，订单号：{}", orderNo);
            throw new BizException("获卡失败");
        }

        return couponCards;
    }

    /**
     * 更新订单状态和发货状态
     */
    private void updateOrderToDelivered(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("卡券订单发货状态更新成功，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }

    /**
     * 保存卡券发货信息到t_order_delivery表
     */
    private void saveCouponDeliveryInfo(OrderInfoEntity orderInfo, List<CouponCardEntity> couponCards) {
        log.info("[数据保存]开始保存卡券发货信息，订单号：{}，卡券数量：{}", orderInfo.getOrderNo(), couponCards.size());

        // 订单项记录
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);

        // 为每张卡券创建一条发货记录
        List<OrderDeliveryEntity> deliveryEntities = couponCards.stream().map(couponCard -> {
            OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

            // 基本订单信息
            deliveryEntity.setOrderId(orderInfo.getId());
            deliveryEntity.setOrderNo(orderInfo.getOrderNo());
            deliveryEntity.setOrderItemId(orderItem.getId());
            deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());

            // 发货信息
            deliveryEntity.setDeliveryType(1); // 1-卡券发放
            deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
            deliveryEntity.setDeliveryTime(LocalDateTime.now());

            // 卡券信息
            deliveryEntity.setCouponVerificationType(couponCard.getType());
            deliveryEntity.setCouponCode(couponCard.getCardCode());
            deliveryEntity.setCouponPin(couponCard.getCardPass());
            deliveryEntity.setCouponCrc(couponCard.getCrc());
            deliveryEntity.setCouponUrl(couponCard.getShortUrl());
            deliveryEntity.setCouponUrlPass(couponCard.getShortUrlPass());
            deliveryEntity.setCouponBatchNo(couponCard.getBatchNum());
            deliveryEntity.setValidDate(couponCard.getValidTime());

            // 收货信息（从订单信息复制）
            deliveryEntity.setReceiverName(orderInfo.getReceiverName());
            deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
            deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());
            deliveryEntity.setRechargeAccount(orderInfo.getRechargeAccount());

            // 发货备注
            deliveryEntity.setDeliveryRemark("卡券自动发放");

            return deliveryEntity;
        }).toList();

        try {
            // 批量保存发货记录
            orderDeliveryRepository.batchSaveOrderDelivery(deliveryEntities);
            log.info("[数据保存]卡券发货信息保存成功，订单号：{}，保存{}条发货记录",
                    orderInfo.getOrderNo(), deliveryEntities.size());
        } catch (Exception e) {
            log.error("[数据保存]卡券发货信息保存失败，订单号：{}，错误信息：{}",
                    orderInfo.getOrderNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 处理充值通知
     *
     * @param notifyDTO  通知DTO
     * @param orderInfo  订单信息
     * @param outOrderSn 外部订单号
     * @return 处理结果
     */
    private CouponNotifyResponseDTO processRechargeNotify(CouponOrderCreatedNotifyDTO notifyDTO,
            OrderInfoEntity orderInfo, String outOrderSn) {
        Integer rechargeStatus = notifyDTO.getRechargeStatus();
        log.info("[充值流程]处理充值通知，订单号：{}，充值状态：{}({})，充值账号：{}",
                outOrderSn, rechargeStatus,
                CouponPlatformRechargeStatus.getByCode(rechargeStatus) != null
                        ? CouponPlatformRechargeStatus.getByCode(rechargeStatus).getDescription()
                        : "未知状态",
                notifyDTO.getRechargeAccount());
        if (!needProcessDelivery(orderInfo,outOrderSn)) {
            log.warn("[充值流程]当前订单状态不是充值中或待充值，直接忽略");
            return CouponNotifyResponseDTO.success();
        }

        // 判断充值状态并处理
        if (CouponPlatformRechargeStatus.isRechargeSuccess(rechargeStatus)) {
            // 充值成功，更新订单状态为交易完成，更新发货状态为已充值，写入充值信息到发货表
            log.info("[充值流程]充值成功，开始更新订单状态，订单号：{}", outOrderSn);
            updateOrderForRechargeSuccess(orderInfo, notifyDTO, outOrderSn);
            return CouponNotifyResponseDTO.success();
        } else if (CouponPlatformRechargeStatus.isRechargeFailed(rechargeStatus)) {
            // 充值失败，更新发货状态为充值失败
            log.info("[充值流程]充值失败，更新发货状态为充值失败，订单号：{}", outOrderSn);
            updateOrderForRechargeFailed(orderInfo, outOrderSn);
            return CouponNotifyResponseDTO.success();
        } else {
            log.error("[充值流程]未知的充值状态：{}，订单号：{}", rechargeStatus, outOrderSn);
            throw new BizException("未知的充值状态：" + rechargeStatus);
        }
    }

    /**
     * 处理原有卡券订单创建流程
     * 
     * @param orderInfo  订单信息
     * @param outOrderSn 外部订单号
     * @return 处理结果
     */
    private CouponNotifyResponseDTO processCouponOrderCreated(OrderInfoEntity orderInfo, String outOrderSn) {
        // 如果订单状态不需要处理，直接返回成功
        if (!needProcessDelivery(orderInfo, outOrderSn)) {
            return CouponNotifyResponseDTO.success();
        }

        // 获取订单的卡信息
        List<CouponCardEntity> couponCards = obtainCouponCards(orderInfo, outOrderSn);

        // 更新订单和发货状态
        updateOrderAndDeliveryStatus(orderInfo, couponCards, outOrderSn);

        log.info("卡券订单下单回调处理成功，订单号：{}", outOrderSn);
        return CouponNotifyResponseDTO.success();
    }

    /**
     * 更新订单充值成功状态
     * 
     * @param orderInfo  订单信息
     * @param notifyDTO  通知DTO
     * @param outOrderSn 外部订单号
     */
    private void updateOrderForRechargeSuccess(OrderInfoEntity orderInfo, CouponOrderCreatedNotifyDTO notifyDTO,
            String outOrderSn) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新订单状态为交易完成，发货状态为已充值
            updateOrderToRechargeSuccess(orderInfo);

            // 写入充值信息到t_order_delivery
            saveRechargeDeliveryInfo(orderInfo, notifyDTO);
            OrderItemEntity item = orderInfo.getOrderItems().get(0);
            // 充值账号记录
            RechargeAccount rechargeAccount = new RechargeAccount(orderInfo.getCustomerId(),item.getProductId(),
                    item.getProductType(),1,notifyDTO.getRechargeAccount());
            rechargeAccountHistoryService.save(rechargeAccount);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]充值成功，订单状态更新完成，订单号：{}，充值账号：{}",
                    outOrderSn, notifyDTO.getRechargeAccount());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新订单充值成功状态失败，事务已回滚，订单号：{}，错误信息：{}",
                    outOrderSn, e.getMessage(), e);
            throw new BizException("更新订单充值成功状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新订单状态为充值成功
     */
    private void updateOrderToRechargeSuccess(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("[充值流程]订单状态更新成功，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }

    /**
     * 更新订单充值失败状态
     * 
     * @param orderInfo  订单信息
     * @param outOrderSn 外部订单号
     */
    private void updateOrderForRechargeFailed(OrderInfoEntity orderInfo, String outOrderSn) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新订单发货状态为充值失败
            updateOrderToRechargeFailed(orderInfo);

            // 自动申请售后单
            createAfterSaleForRechargeFailed(orderInfo);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]充值失败，订单发货状态更新完成，已自动申请售后，订单号：{}", outOrderSn);

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新订单充值失败状态失败，事务已回滚，订单号：{}，错误信息：{}",
                    outOrderSn, e.getMessage(), e);
            throw new BizException("更新订单充值失败状态失败：" + e.getMessage());
        }
    }

    /**
     * 充值失败时自动创建售后单
     * 
     * @param orderInfo 订单信息
     */
    private void createAfterSaleForRechargeFailed(OrderInfoEntity orderInfo) {
        log.info("[充值流程]开始为充值失败订单创建售后单，订单号：{}", orderInfo.getOrderNo());
        // 构建售后申请请求
        AfterSaleApplyRequest applyRequest = new AfterSaleApplyRequest();
        applyRequest.setOrderNo(orderInfo.getOrderNo());
        applyRequest.setCustomerId(orderInfo.getCustomerId());
        applyRequest.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode()); // 全额退款
        applyRequest.setAfterSaleQuantity(orderInfo.getOrderItems().get(0).getQuantity());
        applyRequest.setApplyRefundAmount(orderInfo.getPaymentAmount());
        applyRequest.setAfterSaleReason("充值失败");
        applyRequest.setRefundDescription("系统检测到充值失败，自动申请售后退款");
        applyRequest.setCustomerRemark("充值失败自动申请");

        // 调用售后领域服务创建售后单
        AfterSaleEntity afterSale = afterSaleService.applyAfterSale(orderInfo, applyRequest);

        // 保存售后单
        afterSaleRepository.save(afterSale);

        // 创建售后日志
        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                AfterSaleOperationTypeEnum.APPLY_AFTER_SALE.getCode(),
                "充值失败，系统自动申请售后",
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);

        // 更新订单的售后状态
        afterSaleService.syncOrderAfterSaleStatus(afterSale, orderInfo);


        log.info ("[充值流程]充值失败售后单创建成功，订单号：{}，售后单号：{}",
                orderInfo.getOrderNo(), afterSale.getAfterSaleNo());
    }

    /**
     * 更新订单状态为充值失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setAfterSaleStatus(AfterSaleStatusEnum.PENDING_AUDIT.getCode());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("[充值流程]订单发货状态更新为充值失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 保存充值发货信息到t_order_delivery表
     */
    private void saveRechargeDeliveryInfo(OrderInfoEntity orderInfo, CouponOrderCreatedNotifyDTO notifyDTO) {
        log.info("[充值流程]开始保存充值发货信息，订单号：{}，充值账号：{}",
                orderInfo.getOrderNo(), notifyDTO.getRechargeAccount());

        // 订单项记录
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);

        // 创建充值发货记录
        OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

        // 基本订单信息
        deliveryEntity.setOrderId(orderInfo.getId());
        deliveryEntity.setOrderNo(orderInfo.getOrderNo());
        deliveryEntity.setOrderItemId(orderItem.getId());
        deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());

        // 发货信息
        deliveryEntity.setDeliveryType(3); // 3-红包或充值发放
        deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        deliveryEntity.setDeliveryTime(LocalDateTime.now());

        // 充值信息
        deliveryEntity.setRechargeAccount(notifyDTO.getRechargeAccount());

        // 收货信息（从订单信息复制）
        deliveryEntity.setReceiverName(orderInfo.getReceiverName());
        deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
        deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());

        // 发货备注
        deliveryEntity.setDeliveryRemark("充值成功");

        try {
            // 保存发货记录
            orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
            log.info("[充值流程]充值发货信息保存成功，订单号：{}，充值账号：{}",
                    orderInfo.getOrderNo(), notifyDTO.getRechargeAccount());
        } catch (Exception e) {
            log.error("[充值流程]充值发货信息保存失败，订单号：{}，错误信息：{}",
                    orderInfo.getOrderNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 处理套餐子订单通知
     */
    private CouponNotifyResponseDTO processPackageSubOrderNotify(CouponOrderCreatedNotifyDTO notifyDTO, String outOrderSn) {
        // 根据outOrderSn查找子SKU订单信息
        log.info("[数据读取]根据子订单号查找子SKU订单信息，子订单号：{}", outOrderSn);
        // 根据子商品订单号解析出主订单号
        String[] s = outOrderSn.split("_");
        String mainOrderNo = s[0];
        OrderInfoEntity mainOrder = orderRepository.findByOrderNo(mainOrderNo);

        OrderItemEntity orderItem = mainOrder.getOrderItems().get(0);
        List<SubSkuOrderEntity> subSkuOrders = orderItem.getSubSkuOrders();
        SubSkuOrderEntity subSkuOrder = subSkuOrders.stream()
                .filter(o -> o.getSubOrderNo().equals(outOrderSn))
                .findFirst()
                .orElse(null);
        if (subSkuOrder == null) {
            log.error("子SKU订单不存在，子订单号：{}", outOrderSn);
            throw new BizException("子SKU订单不存在");
        }

        // 对比externalOrderNo和orderSn参数是否一致
        if (!notifyDTO.getOrderSn().equals(subSkuOrder.getExternalOrderNo())) {
            log.error("外部订单号不一致，子订单号：{}，通知中的orderSn：{}，子订单中的externalOrderNo：{}",
                    outOrderSn, notifyDTO.getOrderSn(), subSkuOrder.getExternalOrderNo());
            throw new BizException("订单信息不匹配");
        }

        // 验签
        validateSubSkuOrderSign(notifyDTO, outOrderSn, subSkuOrder);

        // 如果是充值通知（有充值状态），则按充值流程处理
        if (notifyDTO.getRechargeStatus() != null) {
            return processSubSkuRechargeNotify(notifyDTO, subSkuOrder, mainOrder);
        }

        // 原有套餐子订单创建流程
        return processSubSkuOrderCreated(subSkuOrder, mainOrder);
    }

    /**
     * 处理单个卡券订单通知（原有逻辑）
     */
    private CouponNotifyResponseDTO processSingleCouponOrderNotify(CouponOrderCreatedNotifyDTO notifyDTO, String outOrderSn) {
        // 校验订单信息
        OrderInfoEntity orderInfo = validateOrderInfo(notifyDTO, outOrderSn);

        // 验签
        validateSign(notifyDTO, outOrderSn, orderInfo);

        // 如果是充值通知（有充值状态），则按充值流程处理
        if (notifyDTO.getRechargeStatus() != null) {
            return processRechargeNotify(notifyDTO, orderInfo, outOrderSn);
        }

        // 原有卡券订单创建流程
        return processCouponOrderCreated(orderInfo, outOrderSn);
    }

    /**
     * 验证子SKU订单签名
     */
    private void validateSubSkuOrderSign(CouponOrderCreatedNotifyDTO notifyDTO, String outOrderSn, SubSkuOrderEntity subSkuOrder) {
        Map<String, Object> parameters = notifyDTO.getRequestBody();
        String sign = CouponPlatformFactory.getCouponPlatform(
                CouponTypeEnum.getByType(subSkuOrder.getCouponType()))
                .getParametersSign(parameters);
        if (sign.isEmpty() || !sign.equals(parameters.get("sign"))) {
            log.error("子SKU订单通知验签失败，子订单号：{} 回调的签名是{},计算得到的签名是{}", 
                     outOrderSn, parameters.get("sign"), sign);
            throw new BizException("验签失败");
        }
    }

    /**
     * 处理子SKU订单创建流程
     */
    private CouponNotifyResponseDTO processSubSkuOrderCreated(SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        // 判断子SKU订单状态是否需要处理发货
        if (!needProcessSubSkuDelivery(subSkuOrder)) {
            return CouponNotifyResponseDTO.success();
        }

        // 获取子SKU订单的卡信息
        List<CouponCardEntity> couponCards = obtainSubSkuCouponCards(subSkuOrder);

        // 更新子SKU订单和发货状态
        updateSubSkuOrderAndDeliveryStatus(subSkuOrder, couponCards, mainOrder);

        // 检查主订单是否所有子SKU都已发货完成
        checkAndUpdateMainOrderStatus(subSkuOrder,mainOrder);

        log.info("套餐子SKU订单下单回调处理成功，子订单号：{}", subSkuOrder.getSubOrderNo());
        return CouponNotifyResponseDTO.success();
    }

    /**
     * 判断子SKU订单是否需要处理发货
     */
    private boolean needProcessSubSkuDelivery(SubSkuOrderEntity subSkuOrder) {
        Integer deliveryStatus = subSkuOrder.getSubSkuDeliveryStatus();
        if (!DeliveryStatusEnum.UNDELIVERED.getCode().equals(deliveryStatus.byteValue())
                && !DeliveryStatusEnum.DELIVERING.getCode().equals(deliveryStatus.byteValue())) {
            log.info("子SKU订单发货状态不是待发货或发货中，跳过处理，子订单号：{}，当前发货状态：{}",
                    subSkuOrder.getSubOrderNo(), DeliveryStatusEnum.getDescriptionByCode(deliveryStatus.byteValue()));
            return false;
        }
        return true;
    }

    /**
     * 获取子SKU订单卡券信息
     */
    private List<CouponCardEntity> obtainSubSkuCouponCards(SubSkuOrderEntity subSkuOrder) {
        log.info("[业务逻辑]开始调用获卡接口发卡，子订单号：{}，卡券类型：{}",
                subSkuOrder.getSubOrderNo(), subSkuOrder.getCouponType());

        CouponTypeEnum couponType = CouponTypeEnum.getByType(subSkuOrder.getCouponType());
        if (couponType == null) {
            log.error("无法识别的卡券类型，子订单号：{}，卡券类型编码：{}",
                    subSkuOrder.getSubOrderNo(), subSkuOrder.getCouponType());
            throw new BizException("无法识别的卡券类型");
        }

        log.info("[第三方服务调用]子sku订单 获取卡券平台策略，卡券类型：{}", couponType.getText());
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);

        // 调用获卡接口获取卡券信息
        return getCouponCards(couponPlatform, subSkuOrder.getExternalOrderNo(), subSkuOrder.getSubOrderNo());
    }


    /**
     * 更新子SKU订单和发货状态
     */
    private void updateSubSkuOrderAndDeliveryStatus(SubSkuOrderEntity subSkuOrder, 
            List<CouponCardEntity> couponCards, OrderInfoEntity mainOrder) {
        log.info("[数据保存]发卡成功，更新子SKU订单发货状态为已发货，子订单号：{}，获取到{}张卡券",
                subSkuOrder.getSubOrderNo(), couponCards.size());

        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新子SKU订单状态
            updateSubSkuOrderToDelivered(subSkuOrder);

            // 保存发货信息
            saveSubSkuCouponDeliveryInfo(subSkuOrder, couponCards,mainOrder);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("子SKU卡券信息已保存到发货表，子订单号：{}，卡券数量：{}", subSkuOrder.getSubOrderNo(), couponCards.size());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("更新子SKU订单和发货状态失败，事务已回滚，子订单号：{}，错误信息：{}", subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            throw new BizException("更新子SKU订单和发货状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新子SKU订单状态为已发货
     */
    private void updateSubSkuOrderToDelivered(SubSkuOrderEntity subSkuOrder) {
        SubSkuOrderEntity updateSubSku = new SubSkuOrderEntity();
        updateSubSku.setId(subSkuOrder.getId());
        updateSubSku.setCustomerId(subSkuOrder.getCustomerId());
        updateSubSku.setSubSkuDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateSubSku.setModTime(LocalDateTime.now());

        orderRepository.updateSubSkuOrder(updateSubSku);

        log.info("子SKU订单发货状态更新成功，子订单号：{}，发货状态：{}",
                subSkuOrder.getSubOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }

    /**
     * 保存子SKU卡券发货信息到t_order_delivery表
     */
    private void saveSubSkuCouponDeliveryInfo(SubSkuOrderEntity subSkuOrder, List<CouponCardEntity> couponCards,
                                              OrderInfoEntity mainOrder) {
        log.info("[数据保存]开始保存子SKU卡券发货信息，子订单号：{}，卡券数量：{}", 
                subSkuOrder.getSubOrderNo(), couponCards.size());

        // 为每张卡券创建一条发货记录
        List<OrderDeliveryEntity> deliveryEntities = couponCards.stream().map(couponCard -> {
            OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

            // 基本订单信息
            deliveryEntity.setOrderId(mainOrder.getId());
            deliveryEntity.setOrderNo(mainOrder.getOrderNo());
            deliveryEntity.setOrderItemId(subSkuOrder.getOrderItemId());
            deliveryEntity.setMiniSkuId(subSkuOrder.getSubSkuId());
            // 发货信息
            deliveryEntity.setDeliveryType(1);
            deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
            deliveryEntity.setDeliveryTime(LocalDateTime.now());

            // 卡券信息
            deliveryEntity.setCouponVerificationType(couponCard.getType());
            deliveryEntity.setCouponCode(couponCard.getCardCode());
            deliveryEntity.setCouponPin(couponCard.getCardPass());
            deliveryEntity.setCouponCrc(couponCard.getCrc());
            deliveryEntity.setCouponUrl(couponCard.getShortUrl());
            deliveryEntity.setCouponUrlPass(couponCard.getShortUrlPass());
            deliveryEntity.setCouponBatchNo(couponCard.getBatchNum());
            deliveryEntity.setValidDate(couponCard.getValidTime());

            // 收货信息
            deliveryEntity.setReceiverName(mainOrder.getReceiverName());
            deliveryEntity.setReceiverMobile(mainOrder.getReceiverMobile());
            deliveryEntity.setReceiverAddress(mainOrder.getReceiverAddress());
            deliveryEntity.setRechargeAccount(subSkuOrder.getRechargeAccount());

            // 发货备注
            deliveryEntity.setDeliveryRemark("套餐子SKU卡券已发放");

            return deliveryEntity;
        }).toList();

        try {
            // 批量保存发货记录
            orderDeliveryRepository.batchSaveOrderDelivery(deliveryEntities);
            log.info("[数据保存]子SKU卡券发货信息保存成功，子订单号：{}，保存{}条发货记录",
                    subSkuOrder.getSubOrderNo(), deliveryEntities.size());
        } catch (Exception e) {
            log.error("[数据保存]子SKU卡券发货信息保存失败，子订单号：{}，错误信息：{}",
                    subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查并更新主订单状态
     */
    private void checkAndUpdateMainOrderStatus(SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        // 查询主订单的所有子SKU订单
        List<SubSkuOrderEntity> allSubSkuOrders = mainOrder.getOrderItems().get(0).getSubSkuOrders();
        // 排除当前的 subSkuOrder 后检查是否所有子SKU都已发货,因为当前的发货已经处理成功了，排除掉检查
        boolean allDelivered;
        if (allSubSkuOrders.size() == 1) {
            allDelivered = true;
        }
        allDelivered = allSubSkuOrders.stream()
                .filter(subSku -> !subSku.getId().equals(subSkuOrder.getId()))
                .allMatch(subSku -> DeliveryStatusEnum.DELIVERED.getCode()
                        .equals(subSku.getSubSkuDeliveryStatus().byteValue()));




        if (allDelivered) {
            // 更新主订单状态为交易成功
            updateOrderToDelivered(mainOrder);
            log.info("套餐主订单所有子SKU已发货完成，更新主订单状态为交易成功，订单号：{}", mainOrder.getOrderNo());

        }
    }

    /**
     * 处理子SKU充值通知
     */
    private CouponNotifyResponseDTO processSubSkuRechargeNotify(CouponOrderCreatedNotifyDTO notifyDTO,
            SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        Integer rechargeStatus = notifyDTO.getRechargeStatus();
        log.info("[充值流程]处理子SKU充值通知，子订单号：{}，充值状态：{}({})，充值账号：{}",
                notifyDTO.getOutOrderSn(), rechargeStatus,
                CouponPlatformRechargeStatus.getByCode(rechargeStatus) != null
                        ? CouponPlatformRechargeStatus.getByCode(rechargeStatus).getDescription()
                        : "未知状态",
                notifyDTO.getRechargeAccount());

        // 判断充值状态并处理
        if (CouponPlatformRechargeStatus.isRechargeSuccess(rechargeStatus)) {
            // 充值成功
            updateSubSkuOrderForRechargeSuccess(subSkuOrder,mainOrder);
            // 检查主订单状态
            checkAndUpdateMainOrderStatus(subSkuOrder,mainOrder);
            return CouponNotifyResponseDTO.success();
        } else if (CouponPlatformRechargeStatus.isRechargeFailed(rechargeStatus)) {
            // 充值失败
            updateSubSkuOrderForRechargeFailed(subSkuOrder, mainOrder);
            return CouponNotifyResponseDTO.success();
        } else {
            log.error("[充值流程]未知的充值状态：{}，子订单号：{}", rechargeStatus, notifyDTO.getOutOrderSn());
            throw new BizException("未知的充值状态：" + rechargeStatus);
        }
    }

    /**
     * 更新子SKU订单充值成功状态
     */
    private void updateSubSkuOrderForRechargeSuccess(SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新子SKU订单状态为已充值
            updateSubSkuOrderToRechargeSuccess(subSkuOrder);

            // 写入充值信息到t_order_delivery
            saveSubSkuRechargeDeliveryInfo(subSkuOrder, mainOrder);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]子SKU充值成功，订单状态更新完成，子订单号：{}，充值账号：{}",
                    subSkuOrder.getSubOrderNo(), subSkuOrder.getRechargeAccount());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新子SKU订单充值成功状态失败，事务已回滚，子订单号：{}，错误信息：{}",
                    subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            throw new BizException("更新子SKU订单充值成功状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新子SKU订单状态为充值成功
     */
    private void updateSubSkuOrderToRechargeSuccess(SubSkuOrderEntity subSkuOrder) {
        SubSkuOrderEntity updateSubSku = new SubSkuOrderEntity();
        updateSubSku.setId(subSkuOrder.getId());
        updateSubSku.setCustomerId(subSkuOrder.getCustomerId());
        updateSubSku.setSubSkuDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateSubSku.setModTime(LocalDateTime.now());

        orderRepository.updateSubSkuOrder(updateSubSku);

        log.info("[充值流程]子SKU订单状态更新成功，子订单号：{}，发货状态：{}",
                subSkuOrder.getSubOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }

    /**
     * 保存子SKU充值发货信息到t_order_delivery表
     */
    private void saveSubSkuRechargeDeliveryInfo(SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        log.info("[充值流程]开始保存子SKU充值发货信息，子订单号：{}，充值账号：{}",
                subSkuOrder.getSubOrderNo(), subSkuOrder.getRechargeAccount());

        // 创建充值发货记录
        OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

        // 基本订单信息
        deliveryEntity.setOrderId(mainOrder.getId());
        deliveryEntity.setOrderNo(mainOrder.getOrderNo());
        deliveryEntity.setOrderItemId(subSkuOrder.getOrderItemId());
        deliveryEntity.setMiniSkuId(subSkuOrder.getSubSkuId());

        // 发货信息
        deliveryEntity.setDeliveryType(3); // 3-红包或充值发放
        deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        deliveryEntity.setDeliveryTime(LocalDateTime.now());

        // 充值信息
        deliveryEntity.setRechargeAccount(subSkuOrder.getRechargeAccount());

        // 收货信息
        deliveryEntity.setReceiverName(mainOrder.getReceiverName());
        deliveryEntity.setReceiverMobile(mainOrder.getReceiverMobile());
        deliveryEntity.setReceiverAddress(mainOrder.getReceiverAddress());

        // 发货备注
        deliveryEntity.setDeliveryRemark("套餐子SKU充值成功");

        try {
            // 保存发货记录
            orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
            log.info("[充值流程]子SKU充值发货信息保存成功，子订单号：{}，充值账号：{}",
                    subSkuOrder.getSubOrderNo(), subSkuOrder.getRechargeAccount());
        } catch (Exception e) {
            log.error("[充值流程]子SKU充值发货信息保存失败，子订单号：{}，错误信息：{}",
                    subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新子SKU订单充值失败状态
     */
    private void updateSubSkuOrderForRechargeFailed(SubSkuOrderEntity subSkuOrder, OrderInfoEntity mainOrder) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新子SKU订单发货状态为充值失败
            updateSubSkuOrderToRechargeFailed(subSkuOrder);

            // 自动申请售后单
            createAfterSaleForSubSkuRechargeFailed(subSkuOrder,mainOrder);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]子SKU充值失败，订单发货状态更新完成，已自动申请售后，子订单号：{}", subSkuOrder.getSubOrderNo());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新子SKU订单充值失败状态失败，事务已回滚，子订单号：{}，错误信息：{}",
                    subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            throw new BizException("更新子SKU订单充值失败状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新子SKU订单状态为充值失败
     */
    private void updateSubSkuOrderToRechargeFailed(SubSkuOrderEntity subSkuOrder) {
        SubSkuOrderEntity updateSubSku = new SubSkuOrderEntity();
        updateSubSku.setId(subSkuOrder.getId());
        updateSubSku.setCustomerId(subSkuOrder.getCustomerId());
        updateSubSku.setSubSkuDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateSubSku.setModTime(LocalDateTime.now());

        orderRepository.updateSubSkuOrder(updateSubSku);

        log.info("[充值流程]子SKU订单发货状态更新成功，子订单号：{}，发货状态：充值失败",
                subSkuOrder.getSubOrderNo());
    }

    /**
     * 子SKU充值失败时自动创建售后单
     */
    private void createAfterSaleForSubSkuRechargeFailed(SubSkuOrderEntity subSkuOrder,OrderInfoEntity mainOrder) {
        log.info("[充值流程]开始为子SKU充值失败订单创建售后单，子订单号：{}", subSkuOrder.getSubOrderNo());


        // 获取主订单的所有子SKU订单
        OrderItemEntity orderItem = mainOrder.getOrderItems().get(0);
        List<SubSkuOrderEntity> allSubSkuOrders = orderItem.getSubSkuOrders();
        if (allSubSkuOrders == null || allSubSkuOrders.isEmpty()) {
            log.error("主订单子SKU订单为空，无法创建售后单，订单号：{}", subSkuOrder.getOrderNo());
            return;
        }

        // 计算退款金额和售后类型
        BigDecimal applyRefundAmount;
        Integer afterSaleType;
        
        if (allSubSkuOrders.size() == 1) {
            // 只有一个子SKU订单，全额退款
            applyRefundAmount = mainOrder.getPaymentAmount();
            afterSaleType = AfterSaleTypeEnum.FULL_REFUND.getCode();
            log.info("[充值流程]主订单只有一个子SKU，申请全额退款，订单号：{}，退款金额：{}", 
                    subSkuOrder.getOrderNo(), applyRefundAmount);
        } else {
            // 多个子SKU订单，按面值比例计算部分退款
            afterSaleType = AfterSaleTypeEnum.PARTIAL_REFUND.getCode();
            
            // 计算所有子SKU的总面值
            BigDecimal totalFaceAmount = allSubSkuOrders.stream()
                    .map(subSku -> subSku.getFaceAmount().multiply(BigDecimal.valueOf(subSku.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 计算当前失败子SKU的总面值
            BigDecimal currentSubSkuTotalFaceAmount = subSkuOrder.getFaceAmount()
                    .multiply(BigDecimal.valueOf(subSkuOrder.getQuantity()));
            
            // 按面值比例分摊退款金额
            applyRefundAmount = mainOrder.getPaymentAmount()
                    .multiply(currentSubSkuTotalFaceAmount)
                    .divide(totalFaceAmount, 2, RoundingMode.HALF_UP);
            
            log.info("[充值流程]主订单有{}个子SKU，按面值比例计算部分退款，订单号：{}，" +
                    "当前子SKU面值：{}，总面值：{}，支付金额：{}，退款金额：{}", 
                    allSubSkuOrders.size(), subSkuOrder.getOrderNo(), 
                    currentSubSkuTotalFaceAmount, totalFaceAmount, 
                    mainOrder.getPaymentAmount(), applyRefundAmount);
        }

        // 构建售后申请请求
        AfterSaleApplyRequest applyRequest = new AfterSaleApplyRequest();
        applyRequest.setOrderNo(mainOrder.getOrderNo());
        applyRequest.setCustomerId(mainOrder.getCustomerId());
        applyRequest.setAfterSaleType(afterSaleType);
        applyRequest.setAfterSaleQuantity(orderItem.getQuantity());
        applyRequest.setApplyRefundAmount(applyRefundAmount);
        applyRequest.setAfterSaleReason("子SKU充值失败");
        applyRequest.setRefundDescription("系统检测到子SKU充值失败，自动申请售后退款");
        applyRequest.setCustomerRemark("子SKU充值失败自动申请");

        // 调用售后领域服务创建售后单
        AfterSaleEntity afterSale = afterSaleService.applyAfterSale(mainOrder, applyRequest);

        // 保存售后单
        afterSaleRepository.save(afterSale);

        // 创建售后日志
        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                AfterSaleOperationTypeEnum.APPLY_AFTER_SALE.getCode(),
                "子SKU充值失败，系统自动申请售后",
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);

        // 更新订单的售后状态
        afterSaleService.syncOrderAfterSaleStatus(afterSale, mainOrder);

        log.info("[充值流程]子SKU充值失败售后单创建成功，子订单号：{}，售后单号：{}，退款金额：{}",
                subSkuOrder.getSubOrderNo(), afterSale.getAfterSaleNo(), applyRefundAmount);
    }

}
