package com.jsrxjt.mobile.biz.region.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.region.dto.request.UserLocationRequest;
import com.jsrxjt.mobile.biz.region.service.RegionCaseService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.entity.RegionTrie;
import com.jsrxjt.mobile.domain.region.gateway.TencentMapGateway;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.api.region.dto.response.AllCityResponse;
import com.jsrxjt.mobile.api.region.dto.response.CityResponse;
import com.jsrxjt.mobile.api.region.dto.response.SearchCityResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RegionCaseServiceImpl implements RegionCaseService {

    private final RegionTrie hanziTrie = new RegionTrie();

    private final RegionTrie pinyinTrie = new RegionTrie();

    private final RegionTrie initialsTrie = new RegionTrie();

    private final Map<Integer, List<Integer>> childrenMap = new HashMap<>();

    private final Map<Integer, Integer> parentMap = new HashMap<>();

    private final Map<String, Set<Integer>> initialGroups = new HashMap<>();

    private final Map<String, Map<String, List<CityResponse>>> groupCache = new HashMap<>();

    private final Pattern CHINESE_PATTERN = Pattern.compile("[\\u4E00-\\u9FFF]+");

    private final RegionRepository regionRepository;

    private final RedisTemplate redisTemplate;

    private final TencentMapGateway tenantMapGateway;

    @PostConstruct
    public void insertAllRegionToTrie() {
        List<RegionEntity> allRegionList = regionRepository.getAllCityAndDistrict();
        for (RegionEntity regionEntity : allRegionList){
            hanziTrie.insert(regionEntity.getRegionName(), regionEntity.getId());
            pinyinTrie.insert(regionEntity.getPinyin(), regionEntity.getId());
            initialsTrie.insert(regionEntity.getInitials(), regionEntity.getId());
            childrenMap.computeIfAbsent(regionEntity.getParentId(), k -> new ArrayList<>()).add(regionEntity.getId());
            parentMap.put(regionEntity.getId(), regionEntity.getParentId());
            String initial = regionEntity.getInitials().substring(0, 1).toLowerCase();
            initialGroups.computeIfAbsent(initial, k -> new HashSet<>()).add(regionEntity.getId());
        }
    }
    
    @Override
    public List<SearchCityResponse> searchCity(String searchQuery) {
        String hanziQuery = searchQuery.replaceAll("\\s+", "");
        String asciiQuery = searchQuery.replaceAll("\\s+", "").toLowerCase();
        boolean isChinese = CHINESE_PATTERN.matcher(searchQuery).find();
        Set<Integer> resultCityIds = new HashSet<>();
        if (isChinese) {
            resultCityIds.addAll(hanziTrie.searchPrefix(hanziQuery));
        } else {
            resultCityIds.addAll(pinyinTrie.searchPrefix(asciiQuery));
            resultCityIds.addAll(initialsTrie.searchPrefix(asciiQuery));
        }
        // 扩展父级和子级区域
        Set<Integer> allRegionIds = new HashSet<>(resultCityIds);
        for (Integer id : resultCityIds) {
            // 子级
            List<Integer> children = childrenMap.getOrDefault(id, Collections.emptyList());
            allRegionIds.addAll(children);
            // 父级
            Integer parentId = parentMap.get(id);
            if (parentId != null && parentId != 0) {
                allRegionIds.add(parentId);
            }
        }
        List<SearchCityResponse> results = new ArrayList<>();
        if (!resultCityIds.isEmpty()) {
            List<RegionEntity> cityResults = redisTemplate.executePipelined((RedisCallback<Void>) connection -> {
                // 执行多个 Redis 命令
                for (Integer id : allRegionIds) {
                    connection.hGet((RedisKeyConstants.REGION_CITY).getBytes(), id.toString().getBytes());
                }
                return null;
            });
            Map<Integer, RegionEntity> allRegionMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(resultCityIds)){
                for (RegionEntity regionEntity : cityResults) {
                    allRegionMap.put(regionEntity.getId(), regionEntity);
                    if (resultCityIds.contains(regionEntity.getId())){
                        SearchCityResponse cityResponse = new SearchCityResponse();
                        BeanUtils.copyProperties(regionEntity, cityResponse);
                        results.add(cityResponse);
                    }
                }
                for (SearchCityResponse response : results){
                    response.setParentName(allRegionMap.get(response.getParentId()).getRegionName());
                    List<Integer> childrenIds = childrenMap.getOrDefault(response.getId(), Collections.emptyList());
                    if (CollectionUtil.isNotEmpty(childrenIds)){
                        List<SearchCityResponse> children = new ArrayList<>();
                        for (Integer childrenId : childrenIds){
                            RegionEntity childEntity = allRegionMap.get(childrenId);
                            if (childEntity != null){
                                SearchCityResponse childResponse = new SearchCityResponse();
                                BeanUtils.copyProperties(childEntity, childResponse);
                                children.add(childResponse);
                            }
                        }
                        response.setChildren(children);
                    }
                }
            }
        }
        return results;
    }

    @Override
    public AllCityResponse allCity() {
        AllCityResponse allCityResponse = new AllCityResponse();
        List<RegionEntity> allHotCity = redisTemplate.opsForList().range(RedisKeyConstants.REGION_HOT_CITY, 0, -1);
        if (CollectionUtil.isEmpty(allHotCity)){
            allHotCity = regionRepository.getAllHotCity();
            if (CollectionUtil.isNotEmpty(allHotCity)){
                redisTemplate.opsForList().rightPushAll(RedisKeyConstants.REGION_HOT_CITY, allHotCity);
                redisTemplate.expire(RedisKeyConstants.REGION_HOT_CITY, 24*60*60, TimeUnit.SECONDS);
            }
        }
        if(CollectionUtil.isNotEmpty(allHotCity)){
            List<CityResponse> hotCityResponse = new ArrayList<>();
            for (RegionEntity regionEntity : allHotCity){
                CityResponse cityResponse = new CityResponse();
                BeanUtils.copyProperties(regionEntity, cityResponse);
                hotCityResponse.add(cityResponse);
            }
            allCityResponse.setHotCity(hotCityResponse);
        }
        String cacheKey = "group:all";
        // Check cache
        Map<String, List<CityResponse>> regionMap = new HashMap<>();
        if (groupCache.containsKey(cacheKey)) {
            log.info("Group cache hit for key: {}", cacheKey);
            regionMap = groupCache.get(cacheKey);
        }else {
            //所有城市和区县id
            Set<Integer> allRegionIds = initialGroups.values().stream()
                    .flatMap(Set::stream)
                    .collect(Collectors.toSet());
            List<RegionEntity> cityResults = redisTemplate.executePipelined((RedisCallback<Void>) connection -> {
                // 执行多个 Redis 命令
                for (Integer id : allRegionIds) {
                    connection.hGet((RedisKeyConstants.REGION_CITY).getBytes(), id.toString().getBytes());
                }
                return null;
            });
            for (RegionEntity regionEntity : cityResults) {
                String prefix = regionEntity.getPinyin().substring(0, 1).toUpperCase();
                CityResponse cityResponse = new CityResponse();
                BeanUtils.copyProperties(regionEntity, cityResponse);
                if (regionMap.containsKey(prefix)){
                    regionMap.get(prefix).add(cityResponse);
                }else {
                    List<CityResponse> cityResponses = new ArrayList<>();
                    cityResponses.add(cityResponse);
                    regionMap.put(prefix, cityResponses);
                }
            }
            groupCache.put(cacheKey,  regionMap);
        }
        List<AllCityResponse.PyPreCityResponse> pyPreCity = new ArrayList<>();
        regionMap.forEach((k, v)->{
            AllCityResponse.PyPreCityResponse pyPreCityResponse = new AllCityResponse.PyPreCityResponse();
            pyPreCityResponse.setPy(k);
            pyPreCityResponse.setCity(v);
            pyPreCity.add(pyPreCityResponse);
        });
        allCityResponse.setPyPreCity(pyPreCity);
        return allCityResponse;
    }

    @Override
    public SearchCityResponse userLocation(UserLocationRequest request) {
        log.info("用户定位请求参数为:{}", JSONUtil.toJsonStr(request));
        String customerLocationKey = null;
        Long customerId = null;
        try {
            //未登录捕获登录异常
            customerId = StpUtil.getLoginIdAsLong();
            customerLocationKey = RedisKeyConstants.REGION_LOCATION_CUSTOMER + customerId;
        }catch (NotLoginException e){

        }
        Integer enablePosition = request.getEnablePosition();
        if (enablePosition == 0){
            //登录且未开启定位删除缓存
            if(customerLocationKey != null && customerId != null){
                redisTemplate.delete(customerLocationKey);
            }
            return null;
        }
        String adCode = tenantMapGateway.getAdcodeByLocation(request.getLat(), request.getLng());
        RegionEntity customerLocation = new RegionEntity();
        boolean locationExists = false;
        if (StringUtil.isNotEmpty(adCode)){
            Object regionId = redisTemplate.opsForHash().get(RedisKeyConstants.REGION_ADCODE, adCode);
            if (regionId != null){
                Integer cityId = (Integer) regionId;
                Object regionObject = redisTemplate.opsForHash().get(RedisKeyConstants.REGION_CITY,  cityId.toString());
                if (regionObject != null){
                    RegionEntity regionEntity = (RegionEntity)regionObject;
                    BeanUtils.copyProperties(regionEntity, customerLocation);
                    locationExists = true;
                }else {
                    log.error("根据id获取区域失败");
                }
            }else {
                log.error("根据adcode获取区域失败");
            }
        }else {
            log.error("获取腾讯adcode失败");
        }
        if (!locationExists || (customerLocation != null && !Objects.equals(customerLocation.getRegionType(), 3))){
            //获取用户区域失败或获取的区域非三级，根据经纬度从数据库查询最近城市
            RegionEntity regionEntity = regionRepository.getNearestCityByLocation(request.getLat(), request.getLng());
            BeanUtils.copyProperties(regionEntity, customerLocation);
        }
        customerLocation.setLat(request.getLat());
        customerLocation.setLng(request.getLng());
        if (customerLocationKey != null && customerId != null){
            //登录用户设置区域缓存
            redisTemplate.opsForValue().set(customerLocationKey, customerLocation);
        }
        SearchCityResponse response = new SearchCityResponse();
        if(customerLocation.getRegionType().equals(2)){
            BeanUtils.copyProperties(customerLocation, response);
        }else if (customerLocation.getRegionType().equals(3)){
            Object parentCityData = redisTemplate.opsForHash().get(RedisKeyConstants.REGION_CITY, customerLocation.getParentId().toString());
            if (parentCityData != null){
                RegionEntity parentCity = (RegionEntity)parentCityData;
                BeanUtils.copyProperties(parentCity, response);
            }
        }
        SearchCityResponse currentLocation = new SearchCityResponse();
        BeanUtils.copyProperties(customerLocation, currentLocation);
        response.setCurrentLocation(currentLocation);
        //获取子级区县
        List<Integer> childrenList = childrenMap.get(response.getId());
        if (CollectionUtil.isNotEmpty(childrenList)){
            List<RegionEntity> cityResults = redisTemplate.executePipelined((RedisCallback<Void>) connection -> {
                // 执行多个 Redis 命令
                for (Integer id : childrenList) {
                    connection.hGet((RedisKeyConstants.REGION_CITY).getBytes(), id.toString().getBytes());
                }
                return null;
            });
            List<SearchCityResponse> childrenResponse = new ArrayList<>();
            for (RegionEntity regionEntity : cityResults) {
                SearchCityResponse childResponse = new SearchCityResponse();
                BeanUtils.copyProperties(regionEntity, childResponse);
                childrenResponse.add(childResponse);
            }
            response.setChildren(childrenResponse);
        }
        return response;
    }

    /**
     * 手动获取区域信息(前端传入3级地址要返回2级和子地址)
     * @param regionId
     * @return {@link SearchCityResponse}
     */
    @Override
    public SearchCityResponse getRegion(Integer regionId) {
        RegionEntity regionEntity = regionRepository.getRegionById(regionId);
        if (regionEntity != null){
            Integer cityId = regionId;
            if (regionEntity.getRegionType().equals(3)){
                cityId = regionEntity.getParentId();
                regionEntity = regionRepository.getRegionById(cityId);
            }
            List<Integer> children = childrenMap.getOrDefault(cityId, Collections.emptyList());
            List<RegionEntity> childResults = redisTemplate.executePipelined((RedisCallback<Void>) connection -> {
                // 执行多个 Redis 命令
                for (Integer id : children) {
                    connection.hGet((RedisKeyConstants.REGION_CITY).getBytes(), id.toString().getBytes());
                }
                return null;
            });
            SearchCityResponse response = BeanUtil.copyProperties(regionEntity, SearchCityResponse.class);
            if (CollectionUtil.isNotEmpty(childResults)){
                response.setChildren(BeanUtil.copyToList(childResults, SearchCityResponse.class));
            }
            return response;
        }
        throw new BizException("区域不存在");
    }

    /**
     * 更新所有区域缓存
     */
    @Override
    public void updateAllRegionCache() {
        List<RegionEntity> allProvince = regionRepository.getAllRegionByRegionType(1);

        List<RegionEntity> allCity = regionRepository.getAllRegionByRegionType(2);

        List<RegionEntity> allDistrict = regionRepository.getAllRegionByRegionType(3);

        List<RegionEntity> allStreet =  regionRepository.getAllRegionByRegionType(4);

        if (CollectionUtil.isNotEmpty(allProvince) && CollectionUtil.isNotEmpty(allCity)
                && CollectionUtil.isNotEmpty(allDistrict) && CollectionUtil.isNotEmpty(allStreet)) {
            //构建子级关系
            Map<Integer, List<Integer>> provinceChildrenMap = buildChildrenMap(allCity);
            Map<Integer, List<Integer>> cityChildrenMap = buildChildrenMap(allDistrict);
            Map<Integer, List<Integer>> districtChildrenMap = buildChildrenMap(allStreet);
            Map<String, RegionEntity> regionCityCache = new HashMap<>();
            Map<String, Integer> regionAdcodeCache = new HashMap<>();
            for (RegionEntity province : allProvince) {
                province.setChildrenIds(provinceChildrenMap.get(province.getId()));
                regionCityCache.put(province.getId().toString(), province);
                regionAdcodeCache.put(province.getAdcode(), province.getId());
            }
            for (RegionEntity city : allCity) {
                city.setChildrenIds(cityChildrenMap.get(city.getId()));
                regionCityCache.put(city.getId().toString(), city);
                regionAdcodeCache.put(city.getAdcode(), city.getId());
            }
            for (RegionEntity district : allDistrict){
                district.setChildrenIds(districtChildrenMap.get(district.getId()));
                regionCityCache.put(district.getId().toString(), district);
                regionAdcodeCache.put(district.getAdcode(), district.getId());
            }
            String regionCityKey = RedisKeyConstants.REGION_CITY;
            redisTemplate.delete(regionCityKey);
            redisTemplate.opsForHash().putAll(regionCityKey,regionCityCache);
            String regionAdcodeKey = RedisKeyConstants.REGION_ADCODE;
            redisTemplate.delete(regionAdcodeKey);
            redisTemplate.opsForHash().putAll(regionAdcodeKey,regionAdcodeCache);
        }
        this.insertAllRegionToTrie();
    }

    private Map<Integer, List<Integer>> buildChildrenMap(List<RegionEntity> allRegion) {
        return allRegion.stream()
                .filter(region -> region.getParentId() != null)
                .collect(Collectors.groupingBy(
                        RegionEntity::getParentId,
                        Collectors.mapping(RegionEntity::getId, Collectors.toList())
                ));
    }
}
