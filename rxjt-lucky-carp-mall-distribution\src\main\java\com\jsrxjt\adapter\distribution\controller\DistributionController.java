package com.jsrxjt.adapter.distribution.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionCustomerInfoRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionRedirectBaseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerBalanceResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerInfoResponseDTO;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.distribution.service.DistributionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 分销控制器
 * <AUTHOR> Fengping
 * @since 2025/3/20
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/distribution")
@Tag(name = "分销业务", description = "分销相关接口")
public class DistributionController {

    private final DistributionService distributionService;

    private final CustomerService customerService;

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查分销服务是否正常运行")
    public String health() {
        return "Distribution service is running";
    }


    @PostMapping("/getRedirectUrl")
    @Operation(summary = "获取应用跳转链接")
    @VerifySign(hasToken = true)
    public BaseResponse<String> getRedirectUrl(@RequestBody @Valid DistributionRedirectBaseDTO requestDTO) {
        return distributionService.getRedirectUrl(requestDTO);
    }

    /**
     * Form 请求：
     *    - 参数：userCode (必须)
     *    - 返回：使用 success 方法
     * Json 请求：
     *    - 参数：userId (必须)
     *    - 返回：使用 success0 方法
     */
    @PostMapping(value = "/vipInfo", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Operation(summary = "用户信息 - Form")
    public ApiResponse<DistributionCustomerInfoResponseDTO> vipInfoForm(DistributionCustomerInfoRequestDTO requestDTO) {
        if (requestDTO.getUserCode() == null || requestDTO.getUserCode().isEmpty()) {
            return ApiResponse.fail("userCode不能为空");
        }
        try {
            Long customerId = Long.valueOf(requestDTO.getUserCode());
            DistributionCustomerInfoResponseDTO responseDTO = customerService.vipInfo(customerId);
            return ApiResponse.success(responseDTO);
        } catch (NumberFormatException e) {
            log.error("userCode格式错误: {}", requestDTO.getUserCode(), e);
            return ApiResponse.fail("userCode格式错误");
        }
    }
    @PostMapping(value = "/vipInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "用户信息 - JSON")
    public ApiResponse<DistributionCustomerInfoResponseDTO> vipInfoJson(@RequestBody DistributionCustomerInfoRequestDTO requestDTO) {
        if (requestDTO.getUserId() == null || requestDTO.getUserId().isEmpty()) {
            return ApiResponse.fail("userId不能为空");
        }
        try {
            Long customerId = Long.valueOf(requestDTO.getUserId());
            DistributionCustomerInfoResponseDTO responseDTO = customerService.vipInfo(customerId);
            return ApiResponse.success0(responseDTO);
        } catch (NumberFormatException e) {
            log.error("userId格式错误: {}", requestDTO.getUserId(), e);
            return ApiResponse.fail("userId格式错误");
        }
    }

    @PostMapping("/getBalance/{appSpuId}")
    @Operation(summary = "用户余额查询")
    public ApiResponse<DistributionCustomerBalanceResponseDTO> getBalance(DistributionCustomerInfoRequestDTO requestDTO, @PathVariable Long appSpuId) {
        if (requestDTO.getUserId() == null || requestDTO.getUserId().isEmpty()) {
            return ApiResponse.fail("userId不能为空");
        }
        return customerService.getBalance(requestDTO, appSpuId);
    }
}