package com.jsrxjt.mobile.biz.customer.service;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.customer.request.*;
import com.jsrxjt.mobile.api.customer.response.*;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionCustomerInfoRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerBalanceResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCustomerInfoResponseDTO;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;

import java.util.List;

public interface CustomerService {

    CustomerResponse login(CustomerLoginRequest request);

    void sendVerificationCode(CustomerSendVerificationCodeRequest request);

    CustomerResponse bindPhone(CustomerBindPhoneRequest request);

    CustomerInfoResponse getCustomerInfo(Long customerId);

    void editCustomerInfo(CustomerEditRequest request);

    /**
     * 新增或修改密码
     */
    void addOrModifyPassword(CustomerPasswordEditRequest request);

    void setFreePassword(CustomerSetFreePasswordRequest request);

    List<CustomerCardResponse> getCardList(CustomerCardRequest request);

    CustomerCheckLoginOffResponse checkIsAllowedLoginOff(CustomerRequest request);

    void deleteAccount(CustomerDeleteAccountRequest request);

    void bindCard(CustomerBindCardRequest request);

    void unbindCard(CustomerUnbindCardRequest request);

    void setPaySort(CustomerSetPaySortRequest request);

    CustomerPaySortResponse getPaySort(Long customerId);

    /**
     * 获取个人中心广告列表
     * @param regionId 三级地址id
     * @return 广告列表
     */
    List<AdvertisementInfoDTO> getPersonalCenterAd(Integer regionId);

    Boolean checkCustomerHasPayPassword(Long customerId);

    LoginOffConfigResponse getLoginOffConfig();

    CustomerEntity getCustomerById(Long customerId);

    /**
     * 根据卡号和卡密查询卡余额（个人中心 用户查询余额 每天3次）
     * @param request
     * @return
     */
    CardBalanceResponse getBalanceByCardNoAndPassword(CardBalanceQueryRequest request);

    /**
     * 根据卡号和卡密查询卡余额(充值专用，无次数限制)
     * @param request
     * @return
     */
    CardBalanceResponse getBalanceByRecharge(CardBalanceQueryRequest request);

    /**
     * 获取用户信息接口（分销专用）
     * @param customerId 用户id
     * @return 用户信息
     */
    DistributionCustomerInfoResponseDTO vipInfo(Long customerId);

    /**
     * 获取用户一次性token
     * @param customerId
     * @return
     */
    String getOnceToken(long customerId);

    /**
     * 根据一次性token获取用户信息
     * @param onceToken
     * @return
     */

    CustomerDetailResponse getCustomerInfoByOnceToken(String onceToken);

    /**
     * 会员卡充值
     * @param request 请求参数
     * @return 充值结果
     */
    CustomerCardRechargeResponse cardRecharge(CustomerCardRechargeRequest request);

    /**
     * 获取会员卡交易记录
     * @param request 请求参数
     * @return 分页获取卡交易记录
     */
    PageDTO<CustomerCardTradeResponse> getCardTradeList(CustomerCardTradeRequest request);


    /**
     * 校验会员支付密码
     * @param customerId
     * @param psw
     * @return boolean
     */
    boolean checkCustomerPsw(Long customerId, String psw);


    /**
     * 获取用户余额（分销专用）
     * @param requestDTO 请求参数
     * @param appSpuId 应用spuId
     * @return 余额
     */
    ApiResponse<DistributionCustomerBalanceResponseDTO> getBalance(DistributionCustomerInfoRequestDTO requestDTO, Long appSpuId);

    /**
     * 获取会员卡交易记录
     * @param request 请求参数
     * @return 分页获取卡交易记录
     */
    PageDTO<CustomerCardTradeResponse> getCardRechargeRecordList(CustomerCardRechargeRecordQueryRequest request);

    /**
     * 通用配置查询
     * @param request 查询参数
     * @return 配置信息
     */
    String getConfigByType(CommonConfigQueryRequest request);

    /**
     * 延长token超时时间
     */
    void renewTokenTimeout();

    /**
     * 根据扫码结果获取卡号密码cvv
     * @param request 查询参数
     * @return 配置信息
     */
    ScanRechargeResponse getCardInfoByScanResult(ScanRechargeRequest request);
}
