package com.jsrxjt.mobile.domain.product.service.factory.builder.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductBrandEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductChannelEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.repository.ProductBrandRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductChannelRepository;
import com.jsrxjt.mobile.domain.product.service.factory.builder.ProductItemBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 支付宝/苏西话费类型ProductItem构建器
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/27
 */
@Component("couponAppProductItemBuilder")
@RequiredArgsConstructor
@Slf4j
public class CouponAppProductItemBuilder implements ProductItemBuilder {

    private final AppCouponGoodsRepository appCouponGoodsRepository;

    private final ProductBrandRepository productBrandRepository;

    private final ProductChannelRepository  productChannelRepository;

    @Override
    public ProductItem build(ProductItemId productItemId) {
        Long spuId = productItemId.getSpuId();
        Long skuId = productItemId.getSkuId();

        // 支付宝/苏西话费类型必须有SKU
        if (skuId == null || skuId <= 0) {
            throw new IllegalArgumentException("支付宝/苏西话费类型产品必须指定有效的SKU ID");
        }

        // 1. 查询应用SPU信息
        AppCouponGoodsEntity spuEntity = appCouponGoodsRepository.findAppCouponGoodsById(spuId);
        if (spuEntity == null) {
            throw new IllegalArgumentException("未找到SPU ID为 " + spuId + " 的应用商品信息");
        }

        // 2. 查询应用SKU信息
        AppCouponGoodsSkuEntity skuEntity = appCouponGoodsRepository.findSkuBySkuId(skuId);
        if (skuEntity == null) {
            throw new BizException("未找到SKU ID为 " + skuId + " 的应用SKU信息");
        }

        // 3. 验证SPU和SKU的关联关系
        if (!spuEntity.getAppSpuId().equals(skuEntity.getAppSpuId())) {
            throw new BizException("SKU ID " + skuId + " 不属于 SPU ID " + spuId);
        }

        // 4. 构建ProductItem
        return buildFromAppCouponData(spuEntity, skuEntity);
    }

    private ProductItem buildFromAppCouponData(AppCouponGoodsEntity spuEntity, AppCouponGoodsSkuEntity skuEntity) {
        ProductItem productItem = new ProductItem();

        // 设置标识
        productItem.setSpuId(spuEntity.getAppSpuId());
        productItem.setSkuId(skuEntity.getAppSkuId());
        productItem.setProductType(ProductTypeEnum.COUPON_APP.getType()); // 支付宝/苏西话费类型

        // 根据应用类型设置扁平化类型
        int flatProductType = 400 + spuEntity.getAppType(); // 402:支付宝红包 403:苏西话费
        productItem.setFlatProductType(flatProductType);

        // 设置商品基本信息（从SPU获取）
        productItem.setProductName(spuEntity.getAppSpuName());
        productItem.setProductLogo(spuEntity.getLogoUrl());
        productItem.setImgUrl(spuEntity.getImgUrl());
        productItem.setBrandId(spuEntity.getBrandId());
        ProductBrandEntity brand = productBrandRepository.findById(spuEntity.getBrandId());
        if (brand != null) {
            productItem.setBrandName(brand.getBrandName());
        }
        productItem.setChannelId(spuEntity.getChannelId());
        ProductChannelEntity channel = productChannelRepository.findById(spuEntity.getChannelId());
        if (channel != null) {
            productItem.setChannelName(channel.getChannelName());
        }
        productItem.setFirstCategoryId(spuEntity.getFirstCatId());
        productItem.setCategoryId(spuEntity.getSecondCatId());
        productItem.setStatus((spuEntity.getStatus() == 1) && (skuEntity.getStatus() == 1) ? 1 : 0);
        productItem.setSourceTable("app_coupon_goods");

        // 设置价格和手续费信息（从SKU获取）
        productItem.setPlatformPrice(skuEntity.getPlatformPrice());
        productItem.setDefaultServiceFeePercent(skuEntity.getCommissionFee());
        productItem.setOutProductId(skuEntity.getOutPlatformId());
        productItem.setFaceName(skuEntity.getAmountName());
        productItem.setFaceAmount(skuEntity.getAmount());
        productItem.setCostPrice(skuEntity.getPlatformPrice());

        // 设置库存信息（从SKU获取）
        productItem.setInventory(skuEntity.getInventory());
        productItem.setPayType(spuEntity.getPayType());

        log.info("成功构建支付宝/苏西话费ProductItem: spuId={}, skuId={}, appType={}",
                spuEntity.getAppSpuId(), skuEntity.getAppSkuId(), spuEntity.getAppType());

        return productItem;
    }
}