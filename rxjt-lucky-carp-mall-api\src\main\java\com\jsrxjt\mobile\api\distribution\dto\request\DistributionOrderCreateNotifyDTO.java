package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/7/23 17:22
 * 分销应用创建订单通用参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionOrderCreateNotifyDTO extends DistributionNotifyCommonDTO {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "用户unionId")
    private String unionId;

    @Schema(description = "分销业务中心订单号")
    private String orderNo;

    @Schema(description = "分销业务中心交易号")
    private String tradeNo;

    @Schema(description = "订单金额，单位元，两位小数")
    private String totalAmount;

    @Schema(description = "订单金额，单位元，两位小数")
    private String tradeAmount;

    @Schema(description = "下单时间，UTC时间戳，10位")
    private String orderTime;

    @Schema(description = "订单过期时间 单位：分钟")
    private String orderExpire;

    @Schema(description = "交易时间，UTC时间戳，10位")
    private String tradeTime;

    @Schema(description = "支付超时时间，UTC时间戳，10位")
    private String expireTime;

    @Schema(description = "(叮咚,大润发,卫岗,永辉,清美)支付结果页面(支付结束后的跳转页面),需解码")
    private String resultPageUrl;

    @Schema(description = "(同程)支付结果页面(支付结束后的跳转页面)")
    private String resultUrl;

    @Schema(description = "(叮咚,大润发,永辉，清美)订单详情页,需解码")
    private String detailPageUrl;

    @Schema(description = "(水韵,话费)订单详情页")
    private String detailUrl;

    @Schema(description = "(美团,小象超市)订单详情页")
    private String returnUrl;

    @Schema(description = "屈臣氏订单详情页")
    private String orderDetailUrl;

    @Schema(description = "物美-支付流水号")
    private String paymentNo;

    @Schema(description = "屈臣氏-运费")
    private String freightAmount;

    @Schema(description = "(美团,小象超市)订单类型")
    private String orderType;

    @Schema(description = "大润发扫码付店铺编号")
    private String shopCode;

    @Schema(description = "大润发扫码付店铺名称")
    private String shopName;
}
