package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.geo.GeoUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.CommomShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantRxShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopDetailResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.MerchantCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.merchant.entity.MerchantShopDataEntity;
import com.jsrxjt.mobile.domain.merchant.gateway.MerchantGateway;
import com.jsrxjt.mobile.domain.merchant.request.MerchantShopRequest;
import com.jsrxjt.mobile.domain.merchant.service.MerchantService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.shop.entity.ShopAgentEntity;
import com.jsrxjt.mobile.domain.shop.repository.ShopAgentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 商户服务
 * @Author: ywt
 * @Date: 2025-05-27 14:27
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MerchantCaseServiceImpl implements MerchantCaseService {
    private final MerchantGateway merchantGateway;
    private final AppGoodsRepository appGoodsRepository;
    private final RegionRepository regionRepository;
    private final MerchantService merchantService;
    private final ShopAgentRepository shopAgentRepository;

    @Override
    public PageDTO<MerchantShopResponseDTO> getNearShopList(MerchantShopRequestDTO requestDTO) {
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPageSize() < 1) {
            requestDTO.setPageSize(10);
        }
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(requestDTO.getAppId());
        if (Objects.isNull(appGoodsEntity)) {
            log.error("获取附近商户门店，没有查询到扫码付应用：" + requestDTO.getAppId());
            throw BizException.NOT_FIND_SCAN_APP;
        }
        if (Objects.isNull(appGoodsEntity.getThirdId())) {
            log.error("获取附近商户门店，没有查询到扫码付应用：" + requestDTO.getAppId());
            throw BizException.SCAN_APP_THIRD_ID_IS_NULL;
        }
        String lat = null;
        String lng = null;
        try {
            RegionEntity region = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
            lat = region.getLat();
            lng = region.getLng();
        } catch (Exception e) {

        }
        RegionEntity regionEntity = regionRepository.getRegionById(requestDTO.getRegionId());
        if (Objects.isNull(regionEntity) || StringUtils.isEmpty(regionEntity.getAdcode())) {
            log.error("获取附近商户门店，没有查询到对应的城市码：" + requestDTO.getRegionId());
            throw BizException.REGION_ERROR;
        }
        if (Objects.equals(regionEntity.getRegionType(), 3)) {
            regionEntity = regionRepository.getRegionById(regionEntity.getParentId());
        }
        if (StringUtils.isNotEmpty(lat) && StringUtils.isNotEmpty(lng)) {
            regionEntity.setLng(lng);
            regionEntity.setLat(lat);
        }
        // 应用类型
        Integer type = appGoodsEntity.getType();
        switch (type) {
            //首页提货码
            case 6:
                return getHomeScanShopList(requestDTO, regionEntity);
            //提货分销码
            case 4:
                return getPickShopList(requestDTO, regionEntity, appGoodsEntity.getThirdId());
            default:
                return PageDTO.<MerchantShopResponseDTO>builder()
                        .records(Collections.emptyList())
                        .total(0L)
                        .pages(0L)
                        .current(requestDTO.getPage().longValue())
                        .size(requestDTO.getPageSize().longValue())
                        .build();
        }
    }

    @Override
    public List<MerchantShopResponseDTO> getCommonShopList(CommomShopRequestDTO request) {
        RegionEntity region = regionRepository.getRegionById(request.getRegionId());
        if (Objects.isNull(region)) {
            throw new BizException("地址不存在");
        }
        if (region.getRegionType() <= 1 || region.getRegionType() >= 4) {
            throw new BizException("地址错误，只能传二级或三级地址");
        }
        if (region.getRegionType() == 3) {
            region = regionRepository.getRegionById(region.getParentId());
            if (Objects.isNull(region)) {
                throw new BizException("获取二级地址错误");
            }
        }
        //根据父地址id获取所有字地址
        List<RegionEntity> cityEntityList = regionRepository.getRegionsByParentRegionId(region.getId());
        if (CollectionUtil.isEmpty(cityEntityList)) {
            throw new BizException("获取用户所在城市的三级地址信息失败");
        }
        List<String> adcodeList = cityEntityList.stream().map(RegionEntity::getAdcode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(adcodeList)) {
            throw new BizException("获取用户所在城市的三级地址行政编码失败");
        }

        RegionEntity regionEntity = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
        if (Objects.isNull(regionEntity) || StringUtils.isEmpty(regionEntity.getAdcode())
                || StringUtils.isEmpty(regionEntity.getLat()) || StringUtils.isEmpty(regionEntity.getLng())) {
            throw new BizException("获取用户定位信息失败");
        }

        List<ShopAgentEntity> shopAgentEntityList = shopAgentRepository.getCommonShopList(adcodeList, regionEntity.getLng(), regionEntity.getLat());
        if (CollectionUtil.isEmpty(shopAgentEntityList)) {
            return Collections.EMPTY_LIST;
        }
        List<MerchantShopResponseDTO> responseDTOList = new ArrayList<>();
        shopAgentEntityList.forEach(item -> {
            MerchantShopResponseDTO responseDTO = new MerchantShopResponseDTO();
            responseDTO.setId(item.getId().intValue());
            responseDTO.setThird_id(item.getThirdId());
            responseDTO.setBrand_id(item.getBrandId());
            responseDTO.setGroup_id(item.getGroupId());
            responseDTO.setName(item.getName());
            responseDTO.setLat(item.getLat());
            responseDTO.setLng(item.getLng());
            responseDTO.setAddress(item.getAddress());
            responseDTO.setSpecial_logo(item.getSpecialLogo());
            responseDTO.setIs_th_shop(item.getIsThShop());
            responseDTO.setIs_th_open(item.getIsThOpen());
            responseDTO.setTag(item.getTag());
            responseDTO.setDistance(item.getDistance());
            responseDTOList.add(responseDTO);
        });
        return responseDTOList;
    }


    public static void main(String[] args) {
        double dis = GeoUtil.getDistance(31.98514, 118.733348, 35.405228, 116.58914);
        System.out.println("距离为：" + dis);
    }


    @Override
    public PageDTO<MerchantShopResponseDTO> getRxShopList(MerchantRxShopRequestDTO requestDTO) {
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPageSize() < 1) {
            requestDTO.setPageSize(10);
        }
        RegionEntity region = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
        MerchantShopRequest request = new MerchantShopRequest();
        BeanUtils.copyProperties(requestDTO, request);
        request.setCityCode(region.getAdcode());
        request.setLongitude(region.getLng());
        request.setLatitude(region.getLat());
        List<MerchantShopDataEntity.MerchantShopEntity> shopEntityList = merchantService.getRxShopList(request);
        if (CollectionUtil.isEmpty(shopEntityList)) {
            return PageDTO.emptyBuild(requestDTO.getPage().longValue(), requestDTO.getPageSize().longValue());
        }
        // 计算总记录数
        int totalCount = shopEntityList.size();
        // 计算总页数
        int totalPage = (totalCount + requestDTO.getPageSize() - 1) / requestDTO.getPageSize();
        // 页码越界处理
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPage() > totalPage) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total((long) totalCount)
                    .pages((long) totalPage)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }
        // 计算起始索引和结束索引
        int fromIndex = (requestDTO.getPage() - 1) * requestDTO.getPageSize();
        int toIndex = Math.min(fromIndex + requestDTO.getPageSize(), totalCount);

        List<MerchantShopDataEntity.MerchantShopEntity> shopList = shopEntityList.subList(fromIndex, toIndex);
        shopList.forEach(item -> {
            double kilometers = item.getDistance() / 1000;
            BigDecimal bd = new BigDecimal(kilometers)
                    .setScale(1, RoundingMode.HALF_UP);
            item.setDistance(bd.doubleValue());
        });
        /*MerchantShopDataEntity shopDataEntity = merchantGateway.getNearRxShopList(request);
        if (Objects.isNull(shopDataEntity) || shopDataEntity.getCount() <= 0 || CollectionUtils.isEmpty(shopDataEntity.getList())) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }*/
        List<MerchantShopResponseDTO> list = BeanUtil.copyToList(shopList, MerchantShopResponseDTO.class);
        PageDTO<MerchantShopResponseDTO> result = PageDTO.build(list, (long) totalCount, requestDTO.getPageSize().longValue(), requestDTO.getPage().longValue());
                /*.records(list)
                .total((long) totalCount)
                .current(requestDTO.getPage().longValue())
                .size(requestDTO.getPageSize().longValue())
                .pages()
                .build();*/
        return result;
    }

    /**
     * @param shopId
     * @return
     */
    @Override
    public MerchantShopDetailResponseDTO getShopDetail(String shopId) {
        MerchantShopDetailResponseDTO dataResponse = merchantGateway.getShopDetail(shopId);
        return dataResponse;
    }

    /**
     * 商户大全门店列表
     *
     * @param request
     * @param region
     * @param thirdId
     * @return {@link PageDTO}<{@link MerchantShopResponseDTO}>
     */
    private PageDTO<MerchantShopResponseDTO> getPickShopList(MerchantShopRequestDTO request, RegionEntity
            region, String thirdId) {
        MerchantShopRequest merchantShopRequest = new MerchantShopRequest();
        BeanUtils.copyProperties(request, merchantShopRequest);
        merchantShopRequest.setThirdId(thirdId);
        merchantShopRequest.setCityCode(region.getAdcode());
        merchantShopRequest.setLongitude(region.getLng());
        merchantShopRequest.setLatitude(region.getLat());
        MerchantShopDataEntity shopDataEntity = merchantGateway.getNearShopList(merchantShopRequest);
        List<MerchantShopResponseDTO> list = BeanUtil.copyToList(shopDataEntity.getList(), MerchantShopResponseDTO.class);
        return PageDTO.build(list, shopDataEntity.getCount().longValue(), merchantShopRequest.getPageSize().longValue(), merchantShopRequest.getPage().longValue());
    }

    /**
     * 本地库门店列表
     *
     * @param requestDTO
     * @param regionEntity
     * @return {@link PageDTO}<{@link MerchantShopResponseDTO}>
     */
    private PageDTO<MerchantShopResponseDTO> getHomeScanShopList(MerchantShopRequestDTO requestDTO, RegionEntity
            regionEntity) {
        return null;
    }
}
