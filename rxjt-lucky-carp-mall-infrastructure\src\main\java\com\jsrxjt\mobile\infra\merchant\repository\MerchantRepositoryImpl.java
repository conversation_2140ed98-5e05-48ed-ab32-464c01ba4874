package com.jsrxjt.mobile.infra.merchant.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.merchant.entity.MerchantShopDataEntity;
import com.jsrxjt.mobile.domain.merchant.repository.MerchantRepository;
import com.jsrxjt.mobile.infra.merchant.mapper.MerchantMapper;
import com.jsrxjt.mobile.infra.merchant.po.MerchantPO;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 商户
 * @Author: ywt
 * @Date: 2025-08-22 16:51
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class MerchantRepositoryImpl implements MerchantRepository {
    private final MerchantMapper merchantMapper;

    @Override
    @Cacheable(cacheNames = "merchat:rx:shop", unless = "#result == null")
    public List<MerchantShopDataEntity.MerchantShopEntity> getRxShopList() {
        List<MerchantPO> list = merchantMapper.selectList(new LambdaQueryWrapper<MerchantPO>()
                .eq(MerchantPO::getTypeId, 1)
                .isNull(MerchantPO::getDeletedAt));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        List<MerchantShopDataEntity.MerchantShopEntity> shopList = new ArrayList<>();
        list.forEach(item -> {
            MerchantShopDataEntity.MerchantShopEntity entity = new MerchantShopDataEntity.MerchantShopEntity();
            entity.setAddress(item.getAddress());
            entity.setSpecial_logo(item.getSpecialLogo());
            entity.setName(item.getName());
            entity.setLat(item.getLat());
            entity.setLng(item.getLng());
            /*entity.setDistance(GeoUtil.getDistance(Double.parseDouble(request.getLatitude()), Double.parseDouble(request.getLongitude()),
                    Double.parseDouble(item.getLat()), Double.parseDouble(item.getLng())));*/
            shopList.add(entity);
        });
        return shopList;
    }
}
