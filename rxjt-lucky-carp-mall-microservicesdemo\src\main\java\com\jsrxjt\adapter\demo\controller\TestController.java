package com.jsrxjt.adapter.demo.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.jsrxjt.adapter.demo.request.OssRequest;
import com.jsrxjt.adapter.demo.request.SmsRequest;
import com.jsrxjt.common.adapter.annotation.BaseResult;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.PropertyFile;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.account.dto.request.AccountTransferDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaGenerateDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.request.CaptchaVerifyRequestDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaResponseDTO;
import com.jsrxjt.mobile.api.captcha.dto.response.CaptchaVerifyResponseDTO;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.PaymentChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateStandardOrderRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderCreatedDTO;
import com.jsrxjt.mobile.api.payment.dto.request.PaymentCallbackRequestDTO;
import com.jsrxjt.mobile.api.user.dto.UserDTO;
import com.jsrxjt.mobile.api.user.dto.request.UserRequestDTO;
import com.jsrxjt.mobile.biz.account.TransferCaseService;
import com.jsrxjt.mobile.biz.captcha.CaptchaCaseService;
import com.jsrxjt.mobile.biz.mq.QueueSenderService;
import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.biz.oss.UploadImageService;
import com.jsrxjt.mobile.biz.payment.service.PaymentCallbackCaseService;
import com.jsrxjt.mobile.biz.sms.SmsSendService;
import com.jsrxjt.mobile.biz.user.service.UserCaseService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.ThirdPartyExampleOrderInfoBuilder;
import com.jsrxjt.mobile.domain.user.entity.UserEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * TestController
 * 
 * <AUTHOR> Fengping
 *         2023/3/1 16:06
 * 
 **/
@RestController
@RequestMapping("/v1/test")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "测试接口", description = "测试demo相关的演示接口")
public class TestController {

    private final QueueSenderService queueSenderService;

    private final UploadImageService uploadImageService;

    private final SmsSendService smsSendService;

    private final PropertyFile propertyFile;

    private final UserCaseService userCaseService;

    private final TransferCaseService transferCaseService;

    private final PaymentCallbackCaseService paymentCallbackCaseService;

    private final OrderCaseService orderCaseService;
    private final ThirdPartyExampleOrderInfoBuilder thirdPartyExampleOrderInfoBuilder;

    private final CaptchaCaseService captchaCaseService;

    @PostMapping("/hello-sign")
    @Operation(summary = "测试验签", tags = { "测试接口" })
    @VerifySign
    public BaseResponse<String> helloSign(@RequestBody @Valid UserRequestDTO userRequestDTO) {
        log.info("helloSign:{}", userRequestDTO);
        return BaseResponse.succeed("sign is verified successfully");
    }

    @PostMapping("/hello-login")
    @Operation(summary = "测试登录", tags = { "测试接口" })
    public BaseResponse<SaTokenInfo> helloLogin(@RequestBody @Valid UserRequestDTO userRequestDTO) {
        log.info("helloLogin:{}", userRequestDTO);
        StpUtil.login(userRequestDTO.getUserId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        log.info("helloLogin token: {}", JSON.toJSONString(tokenInfo));
        return BaseResponse.succeed(tokenInfo);
    }

    @PostMapping("/hello-auth")
    @VerifySign(hasToken = true)
    @Operation(summary = "测试认证", tags = { "测试接口" })
    public BaseResponse<Object> helloAuth(@RequestBody @Valid UserRequestDTO userRequestDTO) {
        log.info("helloAuth: 登录id是 {}", StpUtil.getLoginId());
        return BaseResponse.succeed(StpUtil.getLoginId());
    }

    @GetMapping("/user/{id}")
    @Operation(summary = "根据userId获取user对象", tags = { "测试接口" })
    public BaseResponse<UserDTO> getUser(@PathVariable("id") Long userId) {
        UserEntity user = userCaseService.getUser(userId);
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);
        return BaseResponse.succeed(userDTO);
    }

    @PostMapping("/users")
    @Operation(summary = "条件查询用户列表", tags = { "测试接口" })
    public BaseResponse<List<UserEntity>> findUsers(@RequestBody @Valid UserRequestDTO userRequestDTO) {
        log.info("userRequestDTO:{}", userRequestDTO);
        return BaseResponse.succeed(userCaseService.listUser(userRequestDTO));
    }

    @PostMapping("/page-users")
    @Operation(summary = "分页查询用户列表", tags = { "测试接口" })
    public BaseResponse<PageDTO<UserEntity>> pageUser(@RequestBody @Valid UserRequestDTO userRequestDTO) {
        log.info("userRequestDTO:{}", userRequestDTO);
        return BaseResponse.succeed(userCaseService.pageUser(userRequestDTO));
    }

    @PostMapping("/account-transfer")
    @Operation(summary = "跨币种转账接口", tags = { "测试接口" })
    @BaseResult
    public void transferAccount(@RequestBody AccountTransferDTO request) {
        transferCaseService.transfer(request);

    }

    @GetMapping("send-user-message-delay")
    public String sendUserMessageDelay() {
        UserDTO user = new UserDTO();
        user.setUserId(1L);
        user.setUserName("测试用户");
        user.setModTime(LocalDateTime.now());
        queueSenderService.send(DestinationConstants.DestinationName.TEST_USER, JSON.toJSONString(user), 60 * 1000L);
        return "发送成功";
    }

    @PostMapping("upload")
    public BaseResponse<Object> uploadImg(@RequestBody @Valid OssRequest request) {
        String split = "/";
        String path = "upload/image/" + RandomUtil.randomNumbers(10) + split + System.currentTimeMillis()
                + request.getFileName();
        return uploadImageService.uploadImage(path, request.getData());
    }

    @GetMapping("/test-id/{id}")
    @SentinelResource("getId")
    public long getId(@PathVariable Long id) {
        log.info("id:{}", id);
        return 0L;
    }

    @GetMapping("/test-id-degrade/{id}")
    @SentinelResource(value = "getId2", fallback = "getId2Fallback")
    public long getIdDegrade(@PathVariable Long id) {
        log.info("id:{}", id);
        return id;
    }

    public long getId2Fallback(Long id) {
        log.info("getId2Fallback:{}", id);
        return 0L;
    }

    @BaseResult
    @PostMapping("/sms-verify-code-send")
    public boolean sendMsg(@RequestBody SmsRequest request) {
        return smsSendService.sendVerifyCode(request.getPhoneNumber(), request.getCode());

    }

    @BaseResult
    @GetMapping("/config-properties")
    public String getPropertyFileConfig(@RequestParam String key) {
        return propertyFile.getStringByKey(key);
    }

    @PostMapping("/notify")
    @Operation(summary = "mock支付宝回调通知")
    public String alipayCallback(HttpServletRequest request) {
        try {
            log.info("mock接收到支付宝回调请求");

            PaymentCallbackRequestDTO requestDTO = new PaymentCallbackRequestDTO();
            requestDTO.setPaymentChannel(PaymentChannelEnum.ALIPAY);
            requestDTO.setHttpServletRequest(request);

            // 处理支付回调，如果成功则正常返回，失败则抛异常
            paymentCallbackCaseService.handlePaymentCallback(requestDTO);

            // 支付宝期望的成功响应
            log.info("mock支付宝回调处理成功");
            return "success";

        } catch (BizException e) {
            log.error("mock支付宝回调业务异常: {}", e.getMessage());
            return "failure"; // 支付宝期望的失败响应
        } catch (Exception e) {
            log.error("mock支付宝回调系统异常", e);
            return "failure";
        }
    }

    /**
     * 第三方订单推单样例
     */
    @PostMapping("/extern-order-submit")
    public BaseResponse<Void> submitThirdPartyOrder(@RequestBody @Valid CreateOrderDTO request) {
        log.info("接收到第三方推单请求，外部订单号：{}", request.getExternalOrderNo());
        orderCaseService.submitOrder(request, thirdPartyExampleOrderInfoBuilder);
        return BaseResponse.succeed();
    }

    @PostMapping("/normal-order-submit")
    public BaseResponse<OrderCreatedDTO> submitOrder(@RequestBody @Valid CreateStandardOrderRequestDTO request) {
        CreateOrderDTO dto = new CreateOrderDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setCustomerId(1115965283836637203L);
        OrderInfoEntity order = orderCaseService.submitOrder(dto);
        OrderCreatedDTO result = new OrderCreatedDTO();
        BeanUtils.copyProperties(order, result);
        return BaseResponse.succeed(result);
    }

    @PostMapping("/captcha/get")
    @Operation(summary = "获取验证码", tags = { "测试接口" })
    public BaseResponse<CaptchaResponseDTO> getCaptcha(@RequestBody @Valid CaptchaRequestDTO request,
                                                       HttpServletRequest servletRequest) {
        log.info("获取验证码请求：类型={}", request.getCaptchaType());
        CaptchaGenerateDTO generateDTO = new CaptchaGenerateDTO();
        generateDTO.setCaptchaType(request.getCaptchaType());
        generateDTO.setBrowserInfo(getRemoteId(servletRequest));
        CaptchaResponseDTO response = captchaCaseService.getCaptcha(generateDTO);
        return BaseResponse.succeed(response);
    }

    @PostMapping("/captcha/verify")
    @Operation(summary = "校验验证码", tags = { "测试接口" })
    public BaseResponse<CaptchaVerifyResponseDTO> verifyCaptcha(@RequestBody @Valid CaptchaVerifyRequestDTO request,
                                                                HttpServletRequest servletRequest) {
        log.info("校验验证码请求：类型={}, token={}", request.getCaptchaType(), request.getToken());
        request.setBrowserInfo(getRemoteId(servletRequest));
        CaptchaVerifyResponseDTO response = captchaCaseService.verifyCaptcha(request);
        return BaseResponse.succeed(response);
    }

    public static String getRemoteId(HttpServletRequest request) {
        String xfwd = request.getHeader("X-Forwarded-For");
        String ip = getRemoteIpFromXfwd(xfwd);
        String ua = request.getHeader("user-agent");
        if (StringUtils.isNotBlank(ip)) {
            return ip + ua;
        }
        return request.getRemoteAddr() + ua;
    }

    private static String getRemoteIpFromXfwd(String xfwd) {
        if (StringUtils.isNotBlank(xfwd)) {
            String[] ipList = xfwd.split(",");
            return StringUtils.trim(ipList[0]);
        }
        return null;
    }

}