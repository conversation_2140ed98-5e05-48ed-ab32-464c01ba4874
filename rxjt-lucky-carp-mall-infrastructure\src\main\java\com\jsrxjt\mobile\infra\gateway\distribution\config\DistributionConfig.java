package com.jsrxjt.mobile.infra.gateway.distribution.config;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 分销平台配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "distribution")
public class DistributionConfig {

    private ChannelConfig meituan;
    private ChannelConfig dingdong;
    private ChannelConfig rtmart;

    private ChannelConfig cdf;

    private ChannelConfig tuanyou;

    private ChannelConfig xicheng;

    private ChannelConfig shiting;

    private ChannelConfig yonghui;

    private ChannelConfig weigang;

    private ChannelConfig shixing;

    private ChannelConfig tongcheng;

    private ChannelConfig qingmei;

    private ChannelConfig shuiyun;

    private ChannelConfig wumeiBeijing;

    private ChannelConfig wumeiOfflineBeijing;

    private ChannelConfig wumeiTianjin;

    private ChannelConfig wumeiOfflineTianjin;

    private ChannelConfig wumeiHuadong;

    private ChannelConfig wumeiOfflineHuadong;

    private LocalLifeChannelConfig locallife;

    private ChannelConfig walmart;

    private ChannelConfig phonebill;

    private ChannelConfig xiaoxiang;

    private ChannelConfig baisheng;

    private ChannelConfig hema;

    private ChannelConfig watsons;

    private ChannelConfig bailian;

    private ChannelConfig rtmartscanpay;

    private ChannelConfig rxqqg;

    private ChannelConfig eleme;

    private ChannelConfig guangming;

    private PhysicalCardMallConfig physicalcardmall;

    private XcyMallChannelConfig xcyAlibaba;
    private XcyMallChannelConfig xcyKsk;
    private XcyMallChannelConfig xcySm;

    @Data
    public static class ChannelConfig {
        private String appId;
        private String appSecret;
        private String baseUrl;
        private String apiPath;
        private String accessPath = "/endpoint/access";
        private String cashierCallbackPath = "/cashier/callback";
        private String refundCallbackPath = "/refund/callback";
        private String orderQueryPath = "/order/query";
        private String refundQueryPath = "/refund/query";
        private String categoryListPath = "/brand/categoryList";
        private String brandListPath = "/brand/brandList";
        private String shopListPath = "/brand/shopList";
        private int syncRefundFlag = 0;

        private int connectTimeout = 5000;
        private int readTimeout = 10000;

    }

    public String getAppIdByChannelType(DistChannelType channelType) {
        ChannelConfig config = getChannelConfigByType(channelType);
        return config != null ? config.getAppId() : null;
    }

    public ChannelConfig getChannelConfigByType(DistChannelType channelType) {
        return switch (channelType) {
            case MEITUAN -> meituan;
            case DINGDONG -> dingdong;
            case RTMART -> rtmart;
            case TUANYOU -> tuanyou;
            case XICHENG -> xicheng;
            case SHITING -> shiting;
            case YONGHUI -> yonghui;
            case WEIGANG -> weigang;
            case SHIXING -> shixing;
            case TONGCHENG -> tongcheng;
            case QINGMEI -> qingmei;
            case SHUIYUN -> shuiyun;
            case WUMEI_BEIJING -> wumeiBeijing;
            case WUMEI_OFFLINE_BEIJING -> wumeiOfflineBeijing;
            case WUMEI_TIANJIN -> wumeiTianjin;
            case WUMEI_OFFLINE_TIANJIN -> wumeiOfflineTianjin;
            case WUMEI_HUADONG -> wumeiHuadong;
            case WUMEI_OFFLINE_HUADONG -> wumeiOfflineHuadong;
            case LOCALLIFE -> locallife;
            case WALMART -> walmart;
            case PHONEBILL -> phonebill;
            case XIAOXIANG -> xiaoxiang;
            case BAISHENG -> baisheng;
            case HEMA -> hema;
            case WATSONS -> watsons;
            case BAILIAN -> bailian;
            case RTMARTSCANPAY -> rtmartscanpay;
            case RXQQG -> rxqqg;
            case XCY_ALIBABA -> xcyAlibaba;
            case XCY_KSK -> xcyKsk;
            case XCY_SM -> xcySm;
            case ELEME -> eleme;
            default -> null;
        };
    }
}