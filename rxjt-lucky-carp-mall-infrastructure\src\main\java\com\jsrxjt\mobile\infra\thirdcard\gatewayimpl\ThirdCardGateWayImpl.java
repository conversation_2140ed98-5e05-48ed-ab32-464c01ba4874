package com.jsrxjt.mobile.infra.thirdcard.gatewayimpl;

import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.thirdcard.gateway.ThirdCardGateWay;
import com.jsrxjt.mobile.domain.thirdcard.request.CardTradeQueryRequest;
import com.jsrxjt.mobile.domain.thirdcard.response.CardTradeQueryResponse;
import com.jsrxjt.mobile.infra.thirdcard.config.ThirdCardConfig;
import com.jsrxjt.mobile.infra.thirdcard.util.ThirdCardUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/27 15:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdCardGateWayImpl implements ThirdCardGateWay {

    private final ThirdCardConfig thirdCardConfig;

    private final RedisUtil redisUtil;

    private final ThirdCardUtil thirdCardUtil;

    private final HttpClientGateway httpClientGateway;

    /**
     * 密钥缓存key ThirdCardMerchantKey:cardTypeCode
     */
    public static final String THIRD_CARD_MERCHANT_KEY = "ThirdCardMerchantKey:";

    @Override
    public String getMchKey(String cardTypeCode) {
        ThirdCardConfig.CardConfig config = thirdCardConfig.getConfigByCardTypeCode(cardTypeCode);
        // 密钥
        String merchantKey = redisUtil.get(THIRD_CARD_MERCHANT_KEY + cardTypeCode);
        if (StringUtils.isBlank(merchantKey)) {
            try {
                Map<String, Object> baseRequest = thirdCardUtil.getBaseRequest(config);
                String sign = thirdCardUtil.getSignature(baseRequest, config.getKey());
                baseRequest.put("sign", sign);
                String result = httpClientGateway.doPostJson(thirdCardConfig.getHost() + thirdCardConfig.getThirdMchKeyUrl(),
                        JSONObject.toJSONString(baseRequest), thirdCardConfig.getConnectTimeout(), thirdCardConfig.getReadTimeout());
                merchantKey = JSONObject.parseObject(result).getString("key_value");
            } catch (IOException e) {
                log.error("卡系统商户密钥交换接口异常:", e);
            }
            if (StringUtils.isNotBlank(merchantKey)) {
                redisUtil.set(THIRD_CARD_MERCHANT_KEY + cardTypeCode, merchantKey, 330 * 60);
            }
        }

        return merchantKey;
    }

    @Override
    public String getCustomerNameByCardNo(String cardNo, String cardTypeCode) {
        // 密钥
        /*String mchKey = getMchKey(cardTypeCode);
        if (StringUtils.isBlank(mchKey)) {
            return null;
        }*/
        String customerName = null;
        try {
            Map<String, Object> baseRequest = new HashMap<>();
            baseRequest.put("cardNo", cardNo);
            /*String sign = thirdCardUtil.getSignature(baseRequest, mchKey);
            baseRequest.put("sign", sign);*/
            log.info("获取卡系统中卡号对应的公司-cardNo:", cardNo);
            String result = httpClientGateway.doGet(thirdCardConfig.getHost() + thirdCardConfig.getCustomerNameByCardNoUrl(),
                    baseRequest, thirdCardConfig.getConnectTimeout(), thirdCardConfig.getReadTimeout());
            log.info("获取卡系统中卡号对应的公司-响应:", result);
            customerName = JSONObject.parseObject(result).getString("data");
        } catch (Exception e) {
            log.error("卡系统根据卡号查询客户名称接口异常:", e);
        }
        return customerName;
    }

    /**
     *  特殊接口  不需要key参与签名
     */
    @Override
    public CardTradeQueryResponse getRechargeRecordsByCardNo(CardTradeQueryRequest request) {
        try {
            Map<String, Object> baseRequest = thirdCardUtil.getBaseRequest(thirdCardConfig.getConfigByCardTypeCode(request.getCardTypeCode()));
            baseRequest.put("card_id", request.getCardNo());
            baseRequest.put("trade_type", request.getTradeType());
            baseRequest.put("start_time", request.getStartTime());
            baseRequest.put("end_time", request.getEndTime());
            baseRequest.put("page_size", request.getPageSize());
            baseRequest.put("page_no", request.getPageNo());
            String sign = thirdCardUtil.getSignature(baseRequest, null);
            baseRequest.put("sign", sign);
            log.info("查询卡号的交易记录接口请求参数：{}", JSONObject.toJSONString(baseRequest));
            String result = httpClientGateway.doPostJson(thirdCardConfig.getHost() + thirdCardConfig.getCardTradeQueryUrl(),
                    JSONObject.toJSONString(baseRequest), thirdCardConfig.getConnectTimeout(), thirdCardConfig.getReadTimeout());
            return JSONObject.parseObject(result, CardTradeQueryResponse.class);
        } catch (Exception e) {
            log.error("查询卡号的交易记录接口异常:", e);
        }
        return null;

    }
}
