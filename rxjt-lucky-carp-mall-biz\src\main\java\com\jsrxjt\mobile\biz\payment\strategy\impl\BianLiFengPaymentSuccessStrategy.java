package com.jsrxjt.mobile.biz.payment.strategy.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.bianlifeng.gateway.BianLiFengGateway;
import com.jsrxjt.mobile.domain.bianlifeng.request.BianLiFengRechargeRequest;
import com.jsrxjt.mobile.domain.customer.dp.RechargeAccount;
import com.jsrxjt.mobile.domain.customer.service.RechargeAccountHistoryService;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025-10-21
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BianLiFengPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;

    private final OrderDeliveryRepository orderDeliveryRepository;

    private final BianLiFengGateway bianLiFengGateway;

    private final AfterSaleService afterSaleService;

    private final AfterSaleLogService afterSaleLogService;

    private final AfterSaleRepository afterSaleRepository;

    private final AfterSaleLogRepository afterSaleLogRepository;

    private final AppCouponGoodsRepository appCouponGoodsRepository;

    private final PlatformTransactionManager transactionManager;

    private final RechargeAccountHistoryService rechargeAccountHistoryService;

    @Override
    public boolean supports(Integer flatProductType) {
        // 仅支持 flatProductType == 407
        return flatProductType != null && flatProductType == 407;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理便利蜂充值订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());

        // 充值账号记录
        OrderItemEntity item = order.getOrderItems().get(0);
        RechargeAccount rechargeAccount = new RechargeAccount(order.getCustomerId(), item.getProductId(),
                item.getProductType(), 1, order.getRechargeAccount());
        rechargeAccountHistoryService.save(rechargeAccount);

        // 1. 便利蜂下单
        String externalOrderNo = pushBianLiFengOrder(order);

        // 2. 下单成功-更新订单状态为已发货，并记录外部订单号
        //    下单失败-更新订单状态为充值失败，自动生成售后订单
        if (StringUtils.isNotBlank(externalOrderNo)) {
            // 充值成功，更新订单状态为交易完成，更新发货状态为已充值，写入充值信息到发货表
            log.info("[充值流程]充值成功，开始更新订单状态，订单号：{}", externalOrderNo);
            updateOrderForRechargeSuccess(order, externalOrderNo);
        } else {
            // 充值失败，更新发货状态为充值失败
            log.info("[充值流程]充值失败，更新发货状态为充值失败，订单号：{}", order.getOrderNo());
            updateOrderForRechargeFailed(order);
        }
        log.info("便利蜂充值订单支付成功处理完成，订单号：{}，外部订单号：{}",
                order.getOrderNo(), externalOrderNo);
    }

    private String pushBianLiFengOrder(OrderInfoEntity order) {
        AppCouponGoodsSkuEntity skuEntity = appCouponGoodsRepository.findSkuBySkuId(order.getProductSkuId());
        if (skuEntity == null) {
            log.info("未找到SKU ID为 :{} 的应用SKU信息", order.getProductSkuId());
            return null;
        }
        BianLiFengRechargeRequest request = BianLiFengRechargeRequest
                .builder()
                .orderNo(order.getOrderNo())
                .orderDate(LocalDateTimeUtil.format(order.getCreateTime(), DatePattern.NORM_DATETIME_FORMATTER))
                .mobilePhone(order.getRechargeAccount())
                .bachNo(skuEntity.getBatchNumber())
                .build();
        ApiResponse<String> response = bianLiFengGateway.recharge(request);
        if (!Objects.equals(200, response.getStatus()) || StringUtils.isBlank(response.getData())) {
            log.error("便利蜂订单:{},推单失败:{}", order.getOrderNo(), response.getMessage());
            return null;
        }
        log.info("便利蜂推单成功，订单号：{}，扁平化产品类型：{}，外部订单号：{}",
                order.getOrderNo(), order.getFlatProductType(), response.getData());
        return response.getData();
    }

    /**
     * 更新订单充值成功状态
     *
     * @param orderInfo       订单信息
     * @param externalOrderNo 外部订单号
     */
    private void updateOrderForRechargeSuccess(OrderInfoEntity orderInfo, String externalOrderNo) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新订单状态为交易完成，发货状态为已充值
            updateOrderToRechargeSuccess(orderInfo, externalOrderNo);

            // 写入充值信息到t_order_delivery
            saveRechargeDeliveryInfo(orderInfo);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]充值成功，订单状态更新完成，订单号：{}，充值账号：{}",
                    externalOrderNo, orderInfo.getRechargeAccount());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新订单充值成功状态失败，事务已回滚，订单号：{}，错误信息：{}",
                    externalOrderNo, e.getMessage(), e);
            throw new BizException("更新订单充值成功状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新订单状态为充值成功
     */
    private void updateOrderToRechargeSuccess(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);

        log.info("[充值流程]订单状态更新成功，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERED.getDescription());
    }

    /**
     * 更新订单充值失败状态
     *
     * @param orderInfo 订单信息
     */
    private void updateOrderForRechargeFailed(OrderInfoEntity orderInfo) {
        // 定义事务
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = transactionManager.getTransaction(definition);

        try {
            // 更新订单发货状态为充值失败
            updateOrderToRechargeFailed(orderInfo);

            // 自动申请售后单
            createAfterSaleForRechargeFailed(orderInfo);

            // 手动提交事务
            transactionManager.commit(status);
            log.info("[充值流程]充值失败，订单发货状态更新完成，已自动申请售后，订单号：{}", orderInfo.getOrderNo());

        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("[充值流程]更新订单充值失败状态失败，事务已回滚，订单号：{}，错误信息：{}",
                    orderInfo.getOrderNo(), e.getMessage(), e);
            throw new BizException("更新订单充值失败状态失败：" + e.getMessage());
        }
    }

    /**
     * 充值失败时自动创建售后单
     *
     * @param orderInfo 订单信息
     */
    private void createAfterSaleForRechargeFailed(OrderInfoEntity orderInfo) {
        log.info("[充值流程]开始为充值失败订单创建售后单，订单号：{}", orderInfo.getOrderNo());
        // 构建售后申请请求
        AfterSaleApplyRequest applyRequest = new AfterSaleApplyRequest();
        applyRequest.setOrderNo(orderInfo.getOrderNo());
        applyRequest.setCustomerId(orderInfo.getCustomerId());
        applyRequest.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode()); // 全额退款
        applyRequest.setAfterSaleQuantity(orderInfo.getOrderItems().get(0).getQuantity());
        applyRequest.setApplyRefundAmount(orderInfo.getPaymentAmount());
        applyRequest.setAfterSaleReason("充值失败");
        applyRequest.setRefundDescription("系统检测到充值失败，自动申请售后退款");
        applyRequest.setCustomerRemark("充值失败自动申请");

        // 调用售后领域服务创建售后单
        AfterSaleEntity afterSale = afterSaleService.applyAfterSale(orderInfo, applyRequest);

        // 保存售后单
        afterSaleRepository.save(afterSale);

        // 创建售后日志
        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                AfterSaleOperationTypeEnum.APPLY_AFTER_SALE.getCode(),
                "充值失败，系统自动申请售后",
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);

        // 更新订单的售后状态
        afterSaleService.syncOrderAfterSaleStatus(afterSale, orderInfo);


        log.info("[充值流程]充值失败售后单创建成功，订单号：{}，售后单号：{}",
                orderInfo.getOrderNo(), afterSale.getAfterSaleNo());
    }

    /**
     * 更新订单状态为充值失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setAfterSaleStatus(AfterSaleStatusEnum.PENDING_AUDIT.getCode());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("[充值流程]订单发货状态更新为充值失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 保存充值发货信息到t_order_delivery表
     */
    private void saveRechargeDeliveryInfo(OrderInfoEntity orderInfo) {
        log.info("[充值流程]开始保存充值发货信息，订单号：{}，充值账号：{}",
                orderInfo.getOrderNo(), orderInfo.getRechargeAccount());

        // 订单项记录
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);

        // 创建充值发货记录
        OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

        // 基本订单信息
        deliveryEntity.setOrderId(orderInfo.getId());
        deliveryEntity.setOrderNo(orderInfo.getOrderNo());
        deliveryEntity.setOrderItemId(orderItem.getId());
        deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());

        // 发货信息
        deliveryEntity.setDeliveryType(3); // 3-红包或充值发放
        deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        deliveryEntity.setDeliveryTime(LocalDateTime.now());

        // 充值信息
        deliveryEntity.setRechargeAccount(orderInfo.getRechargeAccount());

        // 收货信息（从订单信息复制）
        deliveryEntity.setReceiverName(orderInfo.getReceiverName());
        deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
        deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());

        // 发货备注
        deliveryEntity.setDeliveryRemark("充值成功");

        try {
            // 保存发货记录
            orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
            log.info("[充值流程]充值发货信息保存成功，订单号：{}，充值账号：{}",
                    orderInfo.getOrderNo(), orderInfo.getRechargeAccount());
        } catch (Exception e) {
            log.error("[充值流程]充值发货信息保存失败，订单号：{}，错误信息：{}",
                    orderInfo.getOrderNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

}
