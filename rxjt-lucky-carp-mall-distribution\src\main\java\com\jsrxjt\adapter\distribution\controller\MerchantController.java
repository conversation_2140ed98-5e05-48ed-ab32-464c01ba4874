package com.jsrxjt.adapter.distribution.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.CommomShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantRxShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopDetailResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.MerchantCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 商户大全相关接口的控制器
 * @Author: ywt
 * @Date: 2025-05-27 14:24
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/merchant")
@Tag(name = "商户", description = "商户接口")
public class MerchantController {
    private final MerchantCaseService merchantCaseService;

    @PostMapping("/getNearShopList")
    @Operation(summary = "获取展码付应用的附近门店")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<MerchantShopResponseDTO>> getNearShopList(@RequestBody @Valid MerchantShopRequestDTO requestDTO) {
        return BaseResponse.succeed(merchantCaseService.getNearShopList(requestDTO));
    }

    @PostMapping("/getCommonShopList")
    @Operation(summary = "获取951通用码的附近门店")
    @VerifySign(hasToken = true)
    public BaseResponse<List<MerchantShopResponseDTO>> getCommonShopList(@RequestBody @Valid CommomShopRequestDTO request) {
        return BaseResponse.succeed(merchantCaseService.getCommonShopList(request));
    }

    @GetMapping("/getShopDetail")
    @Operation(summary = "获取门店详情")
    @VerifySign(hasToken = true)
    public BaseResponse<MerchantShopDetailResponseDTO> getShopDetail(@RequestParam String shopId) {
        return BaseResponse.succeed(merchantCaseService.getShopDetail(shopId));

    }

    @PostMapping("/getRxShopList")
    @Operation(summary = "获取全球购门店")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<MerchantShopResponseDTO>> getRxShopList(@RequestBody @Valid MerchantRxShopRequestDTO requestDTO) {
        return BaseResponse.succeed(merchantCaseService.getRxShopList(requestDTO));
    }
}
